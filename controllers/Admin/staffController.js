const staffModel = require("../../models/Admin/staffModel");
const companyModel = require("../../models/Admin/companyModel");
const commonFunction = require("../../assets/common");
const AWS = require("aws-sdk");
const fs = require("fs");
const axios = require("axios");
const FormData = require('form-data');
const ActionLogModel = require("../../models/Admin/ActionLogModel");
const commonModel = require("../../models/Admin/commonModel");
const shipmentTypeForShipmentModel = require("../../models/Admin/shipmentTypeForShipmentModel");

exports.removeStaffIdController = async (request, response) => {
	try {
		const { assignId } = request.body
		let getUserDetails = await commonModel.getUserDetails(request);
		const findAssignWorkerData = await staffModel.getShipmentAssignWorkerData(assignId);
		const staffInfo = await staffModel.getStaffDetailsForCompnay(findAssignWorkerData.staff_id);
		const viewShipmentStage = await shipmentTypeForShipmentModel.viewShipmentStage(findAssignWorkerData.local_shipment_stage_id);
		const data = await staffModel.removeStaffModel(assignId)
		if (data) {
			ActionLogModel.createActionLog({
				platform: "CMS",
				performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
				performed_by_name: getUserDetails.name,
				action_type: "STAGE_REMOVE_FROM_USER",
				action_performed_on_id: assignId,
				action_performed_on_name:  `Shipment - ${viewShipmentStage.shipment_job.job_number}, Stage - ${viewShipmentStage.name}, user - ${staffInfo.first_name} ${staffInfo.last_name}` ,
			})
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, "Worker unassign successfully", {})
		}
	}
	catch (reason) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
}

exports.staffListByCompanyController = async (request, response) => {
	try {
		const { company_id } = request.body;
		const staffListByShipment = await staffModel.staffListByCompanyModel(company_id);
		if (staffListByShipment)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				STAFF_RETRIEVED_SUCCESS,
				staffListByShipment
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				STAFF_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};



exports.staffStorageIdUpdate = async (request, response) => {
	try {
		const staffDetail = await staffModel.staffStorageIdUpdate(request.body);
		if (staffDetail.includes(1)) {
			if (request && request.body && request.body.warehouse_id || request.body.warehouseId) {
				const addStaffWarehouse = await staffModel.addStaffWarehouseById(request.body);
			}
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: "success",
			});
		}
		else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: "fail",
				data: {},
			});
		}
	}
	catch (error) {
		console.log("exports.staffStorageIdUpdate -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}


exports.staffStorageIdUpdateByEmail = async (request, response) => {
	try {
		const staffFind = await staffModel.staffStorageIdUpdateByEmailFind(request.body);
		if (staffFind) {
			if (staffFind.storage_staff_id !== null && staffFind.storage_staff_id !== undefined && staffFind.storage_staff_id !== "") {
				response.status(SUCCESS_CODE).json({
					status: 1,
					message: "success",
				});
			}
			else {
				const staffDetail = await staffModel.staffStorageIdUpdateByEmail(request.body);
				if (staffDetail.includes(1)) {
					const addStaffWarehouse = await staffModel.addStaffWarehouse(request.body, staffFind);
					response.status(SUCCESS_CODE).json({
						status: 1,
						message: "success",
					});
				}
				else {
					response.status(EXPECTATION_FAILED_CODE).json({
						status: 0,
						message: "fail",
						data: {},
					});
				}
			}
		}
		else {
			response.status(SUCCESS_CODE).json({
				status: 1,
				message: "success",
			});
		}
	}
	catch (error) {
		console.log("exports.staffStorageIdUpdateByEmail -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
}

exports.resetPassword = async (request, response) => {
	try {
		let isVerified = await staffModel.verifyToken(request.body.code);
		if (isVerified === 1) {
			let { code, new_password } = request.body;
			let pwdChanged = await staffModel.resetPassword({
				code,
				new_password,
			});
			if (Array.isArray(pwdChanged) && pwdChanged[0] === 1) {
				response.status(SUCCESS_CODE).json({
					status: 1,
					message: PASSWORD_RESET_SUCCESS,
					data: {},
				});
			} else {
				response.status(SUCCESS_CODE).json({
					status: 0,
					message: PASSWORD_RESET_ERROR,
					data: {},
				});
			}
		} else {
			response.status(SUCCESS_CODE).json({
				status: 0,
				message: TOKEN_VERIFICATION_ERROR,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.resetPassword -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.addStaff = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		const emailCheckCompany = await companyModel.checkExistingEmail(
			request.body.email
		);
		if (emailCheckCompany > 0) {
			return response.status(CONFLICT_CODE).json({
				status: 1,
				message: "Email can't be same as company email, so please choose another!. ",
				data: {},
			})
		}
		const fetchCompanyIdForStaff = await companyModel.fetchCompanyIdForStaff(
			request.body.company_id
		);
		request.body.company_identity = fetchCompanyIdForStaff.company_identity

		const emailCount = await staffModel.checkExistingEmail(
			request.body
		);
		if (emailCount > 0) {
			response.status(CONFLICT_CODE).json({
				status: 1,
				message: STAFF_EMAIL_EXIST,
				data: {},
			});
		} else {
			try {
				let newFileName = "";
				let headerData = await commonFunction.jwtTokenDecode(
					request.headers.access_token
				);

				if (request.file && request.file.originalname !== "") {
					const fileName = request.file.filename;
					const fileExt = request.file.originalname.split(".");
					newFileName = fileName + "." + fileExt[1];
					const s3 = new AWS.S3();
					let params = {
						ACL: "public-read",
						Bucket: Const_AWS_BUCKET,
						Body: fs.createReadStream(request.file.path),
						Key: Const_AWS_Staff_Profile + "original/" + newFileName,
					};

					let result = await commonFunction.UploadImageS3(params, s3);
					if (result) {
						fs.unlinkSync(request.file.path);
					}
				}

				const messageCheck = request.body.roles == "WORKER" ? `<p>Please download the app from the App Store or Play Store and use the
				credentials below to log in :</p>` :
					`<p>Please use the below credentials to login:</p>
					<p style='margin-bottom: 8px'> Click here to login : 
					<a href=${CMS_URL_LINK} target="_blank"><B>Mover-Inventory</B></a>
					</p> 
				</p>`

				let html = await commonFunction.readFile("welcomeUser.html");
				html = html.replace(/{{name}}/g, request.body.first_name);
				html = html.replace(/{{companyId}}/g, request.body.company_identity);
				html = html.replace(/{{email}}/g, request.body.email);
				html = html.replace(/{{password}}/g, request.body.password);
				html = html.replace(/{{message}}/g, messageCheck);


				let isEmailSent = await commonFunction.sendEmail(
					request.body.email,
					"Welcome to Mover Inventory",
					html
				);
				if (isEmailSent == false) {
					throw Error("Error in mail send !");
				} else {
					try {
						const staffDetail = await staffModel.addStaff(
							request.body, 
							newFileName
						);

						if (request.body.tag) {
							let tagStaff = [];
							request.body.tag.map((tag) => {
								tagStaff.push({
									staff_id: staffDetail.staff_id,
									warehouse_id: tag,
								})
							}
							);
							await staffModel.addwarehousesToStaffModel(tagStaff, staffDetail.staff_id);
						}

						// Capture affected fields for CREATE operation
						const affectedFields = ActionLogModel.captureAffectedFields(request.body);

						ActionLogModel.createActionLog({
							platform: "CMS",
							performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : headerData.company_id,
							performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
							performed_by_name: getUserDetails.name,
							action_type: "USER_CREATE",
							action_performed_on_id: staffDetail.staff_id,
							action_performed_on_name: staffDetail.first_name,
							affected_fields: affectedFields
						})

						response.status(SUCCESS_CODE).json({
							status: 1,
							message: STAFF_ADD_SUCCESS,
							data: staffDetail,
						});


					} catch (error) {
						response.status(SERVER_ERROR_CODE).json({
							status: 0,
							message: error.message,
							data: {},
						});
					}
				}
			} catch (error) {
				response.status(EXPECTATION_FAILED_CODE).json({
					status: 0,
					message: STAFF_ADD_FAIL,
					data: {},
				});
			}
		}
	} catch (error) {
		console.log("exports.addStaff -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.createSuperAdminStaffController = async (request, response) => {
	try {
		const identityCount = await companyModel.checkExistingIdentity(
			request.body.company_identity
		);
		if (identityCount > 0) {
			response.status(CONFLICT_CODE).json({
				status: 1,
				message: COMPANY_IDENTITY_EXIST,
				data: {},
			});
		}
		else {
			const emailCount = await companyModel.checkExistingEmail(
				request.body.email
			);
			if (emailCount > 0) {
				response.status(CONFLICT_CODE).json({
					status: 1,
					message: COMPANY_EMAIL_EXIST,
					data: {},
				});
			}
			else {
				const emailCountStaff = await staffModel.checkExistingEmail(
					request.body
				);
				if (emailCountStaff > 0) {
					response.status(CONFLICT_CODE).json({
						status: 1,
						message: STAFF_EMAIL_EXIST,
						data: {},
					});
				}
				else {
					const companyDetail = await staffModel.createSuperAdminStaffModel(
						request.body,
					);

					response.status(SUCCESS_CODE).json({
						status: 1,
						message: COMPANY_ADD_SUCCESS,
						data: companyDetail,
					});
				}
			}
		}
	} catch (error) {
		console.log("exports.addCompany -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};



exports.addStaffStorage = async (request, response) => {
	try {

		const getCompanyId = await companyModel.getCompanyIdForStaff(request.body);
		request.body.company_id = getCompanyId.company_id

		const emailCheckCompany = await companyModel.checkExistingEmail(
			request.body.email
		);
		if (emailCheckCompany > 0) {
			return response.status(CONFLICT_CODE).json({
				status: 1,
				message: "Email can't be same as company email, so please choose another!. ",
				data: {},
			})
		}
		const fetchCompanyIdForStaff = await companyModel.fetchCompanyIdForStaff(
			request.body.company_id
		);
		request.body.company_identity = fetchCompanyIdForStaff.company_identity

		const emailCount = await staffModel.checkExistingEmail(
			request.body
		);
		if (emailCount > 0) {
			response.status(CONFLICT_CODE).json({
				status: 1,
				message: STAFF_EMAIL_EXIST,
				data: {},
			});
		} else {
			try {
				let html = await commonFunction.readFile("welcomeUser.html");
				html = html.replace(/{{name}}/g, request.body.first_name);
				html = html.replace(/{{companyId}}/g, request.body.company_identity);
				html = html.replace(/{{email}}/g, request.body.email);
				html = html.replace(/{{password}}/g, request.body.password);

				let isEmailSent = await commonFunction.sendEmail(
					request.body.email,
					"Welcome to Mover Inventory",
					html
				);
				if (isEmailSent == false) {
					throw Error("Error in mail send !");
				} else {
					try {
						const staffDetail = await staffModel.addStaffStorage(
							request.body,
						);

						if (request.body.warehouses) {
							let tagStaff = [];
							request.body.warehouses.map((tag) => {
								tagStaff.push({
									staff_id: staffDetail.staff_id,
									warehouse_id: tag,
								})
							}
							);

							await staffModel.addwarehousesToStaffModel(tagStaff, staffDetail.staff_id);
						}

						response.status(SUCCESS_CODE).json({
							status: 1,
							message: STAFF_ADD_SUCCESS,
							data: staffDetail,
						});
					} catch (error) {
						response.status(SERVER_ERROR_CODE).json({
							status: 0,
							message: error.message,
							data: {},
						});
					}
				}
			} catch (error) {
				response.status(EXPECTATION_FAILED_CODE).json({
					status: 0,
					message: STAFF_ADD_FAIL,
					data: {},
				});
			}
		}
	} catch (error) {
		console.log("exports.addStaffStorage -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.getStaffList = async (request, response) => {
	try {
		try {
			const staffList = await staffModel.getStaffList(
				request.company_id,
				request.body
			);
			if (staffList !== "") {
				response.status(SUCCESS_CODE).json({
					status: 1,
					message: STAFF_RETRIEVED_SUCCESS,
					data: { staffList },
				});
			} else {
				response.status(EXPECTATION_FAILED_CODE).json({
					status: 0,
					message: STAFF_NOT_FOUND,
					data: {},
				});
			}
		} catch (error) {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: STAFF_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.getStaffList -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.getlistAllUsers = async (request, response) => {
	try {
		try {
			const staffList = await staffModel.getlistAllUsers();
			if (staffList !== "") {
				response.status(SUCCESS_CODE).json({
					status: 1,
					message: STAFF_RETRIEVED_SUCCESS,
					data: { staffList },
				});
			} else {
				response.status(EXPECTATION_FAILED_CODE).json({
					status: 0,
					message: STAFF_NOT_FOUND,
					data: {},
				});
			}
		} catch (error) {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: STAFF_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.getlistAllUsers -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.viewStaff = async (request, response) => {
	try {
		const staffDetail = await staffModel.viewStaff(
			request.company_id,
			request.body.staff_id
		);
		if (staffDetail !== "") {
			response.status(SUCCESS_CODE).json({
				message: STAFF_RETRIEVED_SUCCESS,
				data: staffDetail,
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: STAFF_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.viewStaff -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.editStaffStorage = async (request, response) => {
	try {

		const getCompanyId = await companyModel.getCompanyIdForStaff(request.body);
		request.body.company_id = getCompanyId.company_id
		const getStaffForWarehouse = await staffModel.getStaffForWarehouse(request.body);

		const emailCount = await staffModel.checkReExistingEmailForStorage(request.body);
		if (emailCount > 0) {
			response.status(CONFLICT_CODE).json({
				status: 1,
				message: STAFF_EMAIL_EXIST,
				data: {},
			});
		} else {
			try {
				let newFileName = "";
				let headerData = await commonFunction.jwtTokenDecode(
					request.headers.access_token
				);

				if (request.file && request.file.originalname !== "") {
					const oldStaffData = await staffModel.viewStaff(
						request.company_id,
						request.body.staff_id
					);

					const fileName = request.file.filename;
					const fileExt = request.file.originalname.split(".");
					newFileName = fileName + "." + fileExt[1];

					const s3 = new AWS.S3();
					let params = {
						ACL: "public-read",
						Bucket: Const_AWS_BUCKET,
						Body: fs.createReadStream(request.file.path),
						Key: Const_AWS_Staff_Profile + "original/" + newFileName,
					};

					if (oldStaffData.photo) {
						let delMedia = {
							Bucket: Const_AWS_BUCKET,
							Key: Const_AWS_Staff_Profile + "original/" + oldStaffData.photo,
						};
						await commonFunction.deleteImageS3(delMedia, s3);
					}

					let result = await commonFunction.UploadImageS3(params, s3);

					if (result) {
						fs.unlinkSync(request.file.path);
					}
				}

				let html = await commonFunction.readFile("welcome.html");
				html = html.replace(/{{name}}/g, request.body.first_name);
				html = html.replace(/{{email}}/g, request.body.email);
				html = html.replace(/{{password}}/g, request.body.password);
				html = html.replace(/{{cmsUrl}}/g, CMS_URL_LINK);

				let isEmailSent = await commonFunction.sendEmail(
					request.body.email,
					"User edit successfully.",
					html
				);
				if (isEmailSent == false) {
					throw Error("Error in mail send !");
				}
				else {
					const staffDetail = await staffModel.editStaffStorage(
						request.body,
						newFileName
					);
					if (request.body.warehouses) {
						let tagStaff = [];
						request.body.warehouses.map((tag) => {
							tagStaff.push({
								staff_id: getStaffForWarehouse.staff_id,
								warehouse_id: tag,
							})
						}
						);
						await staffModel.addwarehousesToStaffModel(tagStaff, getStaffForWarehouse.staff_id);
					}

					response.status(SUCCESS_CODE).json({
						status: 1,
						message: STAFF_UPDATE_SUCCESS,
						data: {},
					});

				}
			} catch (error) {
				response.status(EXPECTATION_FAILED_CODE).json({
					status: 0,
					message: STAFF_UPDATE_FAIL,
					data: {},
				});
			}
		}
	} catch (error) {
		console.log("exports.editStaffStorage -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.editStaff = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);

		// Get old data before update
		const oldStaffData = await staffModel.viewStaff(null, request.body.staff_id);

		const emailCheckCompany = await companyModel.checkExistingEmail(
			request.body.email
		);
		const emailCount = await staffModel.checkReExistingEmail(request.body);
		if (emailCount > 0) {
			response.status(CONFLICT_CODE).json({
				status: 1,
				message: STAFF_EMAIL_EXIST,
				data: {},
			});
		} else {
			try {
				let newFileName = "";
				let headerData = await commonFunction.jwtTokenDecode(
					request.headers.access_token
				);

				if (request.file && request.file.originalname !== "") {
					const oldStaffData = await staffModel.viewStaff(
						request.company_id,
						request.body.staff_id
					);

					const fileName = request.file.filename;
					const fileExt = request.file.originalname.split(".");
					newFileName = fileName + "." + fileExt[1];

					const s3 = new AWS.S3();
					let params = {
						ACL: "public-read",
						Bucket: Const_AWS_BUCKET,
						Body: fs.createReadStream(request.file.path),
						Key: Const_AWS_Staff_Profile + "original/" + newFileName,
					};

					if (oldStaffData.photo) {
						let delMedia = {
							Bucket: Const_AWS_BUCKET,
							Key: Const_AWS_Staff_Profile + "original/" + oldStaffData.photo,
						};
						await commonFunction.deleteImageS3(delMedia, s3);
					}

					let result = await commonFunction.UploadImageS3(params, s3);

					if (result) {
						fs.unlinkSync(request.file.path);
					}
				}

				let html = await commonFunction.readFile("welcome.html");
				html = html.replace(/{{name}}/g, request.body.first_name);
				html = html.replace(/{{email}}/g, request.body.email);
				html = html.replace(/{{password}}/g, request.body.password);
				html = html.replace(/{{cmsUrl}}/g, CMS_URL_LINK);

				let isEmailSent = await commonFunction.sendEmail(
					request.body.email,
					"User edit successfully.",
					html
				);
				if (isEmailSent == false) {
					throw Error("Error in mail send !");
				}
				else {
					const staffDetail = await staffModel.editStaff(
						request.body,
						newFileName
					);

					if (request.body.tag) {
						let tagStaff = [];
						request.body.tag.map((tag) => {
							tagStaff.push({
								staff_id: request.body.staff_id,
								warehouse_id: tag,
							})
						}
						);
						await staffModel.addwarehousesToStaffModel(tagStaff, request.body.staff_id);
					}

					// Capture both old and new values for UPDATE operation
					const affectedFields = ActionLogModel.captureUpdateFields(oldStaffData, request.body);

					ActionLogModel.createActionLog({
						platform: "CMS",
						performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
						performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
						performed_by_name: getUserDetails.name,
						action_type: "USER_UPDATE",
						action_performed_on_id: request.body.staff_id,
						action_performed_on_name: request.body.first_name,
						affected_fields: affectedFields
					})

					response.status(SUCCESS_CODE).json({
						status: 1,
						message: STAFF_UPDATE_SUCCESS,
						data: {},
					});

				}
			} catch (error) {
				response.status(EXPECTATION_FAILED_CODE).json({
					status: 0,
					message: STAFF_UPDATE_FAIL,
					data: {},
				});
			}
		}
	} catch (error) {
		console.log("exports.editStaff -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.consumerLoginJsonDelete = async (request, response, integration_key) => {
	const consumerLoginJson = JSON.stringify({
		companyIdTokenMoverInventory: integration_key,
		email: "<EMAIL>",
		password: "5PLaRAqq",
		deviceToken: "abcd",
		deviceType: 0,
	});
	try {
		const consumerLoginResponse = await axios.post(`${MOVER_STORAGE_API_URL}login`, consumerLoginJson, {
			headers: {
				'Content-Type': 'application/json'
			}
		})
		if (consumerLoginResponse.data !== "" && consumerLoginResponse.data !== undefined && consumerLoginJson.data !== null) {
			return consumerLoginResponse.data;
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {});
	}
}

exports.staffStatusJson = async (request, response, fetchStaffForStorage, consumerLoginJson) => {
	const staffId = fetchStaffForStorage.storage_staff_id
	const staffStatusJson = JSON.stringify({
		isDeleted: false,
		isActive: fetchStaffForStorage.status == "active" ? 1 : 0
	});

	try {
		const staffStatusResponse = await axios.put(`${MOVER_STORAGE_API_URL}import/mover-inventory/users/active-status/${staffId}`, staffStatusJson, {
			headers: {
				'Content-Type': 'application/json',
				'accessToken': consumerLoginJson.data.accessToken,
			}
		})
		if (staffStatusResponse.status == 200) {
			return staffStatusResponse
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Staff status fail 1", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Staff status fail 2", {});
	}
}

exports.changeStaffStatus = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		let getStaffDetailsForCompnay = await staffModel.getStaffDetailsForCompnay(request.body.staff_id);
		const staffDetail = await staffModel.changeStaffStatus(request.body);
		if (staffDetail.includes(1)) {
			ActionLogModel.createActionLog({
				platform: "CMS",
				performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
				performed_by_name: getUserDetails.name,
				action_type: getStaffDetailsForCompnay.status == "active" ? "USER_DEACTIVATE" : "USER_ACTIVATE",
				action_performed_on_id: request.body.staff_id,
				action_performed_on_name: `${getStaffDetailsForCompnay.first_name} ${getStaffDetailsForCompnay.last_name}`,
			})
			response.status(SUCCESS_CODE).json({
				message: STATUS_CHANGE_SUCCESS,
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: STATUS_CHANGE_FAIL,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.changeStaffStatus -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.changeStaffStatusStorage = async (request, response) => {
	try {
		const staffDetail = await staffModel.changeStaffStatusStorage(request.body);
		if (staffDetail.includes(1)) {
			response.status(SUCCESS_CODE).json({
				status: 0,
				message: STATUS_CHANGE_SUCCESS,
				data: {},
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				status: 0,
				message: STATUS_CHANGE_FAIL,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.changeStaffStatusStorage -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.staffDeleteJson = async (request, response, fetchStaffForStorage, consumerLoginJson) => {
	const staffId = fetchStaffForStorage.storage_staff_id
	const staffDeleteJson = JSON.stringify({
		isDeleted: true,
		isActive: 0
	});
	try {
		const staffDeleteResponse = await axios.put(`${MOVER_STORAGE_API_URL}import/mover-inventory/users/active-status/${staffId}`, staffDeleteJson, {
			headers: {
				'Content-Type': 'application/json',
				'accessToken': consumerLoginJson.data.accessToken,
			}
		})
		if (staffDeleteResponse.status == 200) {
			return staffDeleteResponse
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Staff delete fail", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Staff delete fail", {});
	}
}

exports.deleteStaff = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		let getStaffDetailsForCompnay = await staffModel.getStaffDetailsForCompnay(request.body.staff_id);
		const installedUserDetails = await staffModel.deleteAccessToken(request.body);
		const staffDetail = await staffModel.deleteStaff(request.body);
		// const deleteStaffShipmentDelete = await staffModel.deleteStaffShipmentDelete(request.body);
		if (staffDetail.includes(1)) {
			ActionLogModel.createActionLog({
				platform: "CMS",
				performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
				performed_by_name: getUserDetails.name,
				action_type: "USER_DELETE",
				action_performed_on_id: request.body.staff_id,
				action_performed_on_name: `${getStaffDetailsForCompnay.first_name} ${getStaffDetailsForCompnay.last_name}`,
			})
			response.status(SUCCESS_CODE).json({
				message: STAFF_DELETE_SUCCESS,
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: STAFF_DELETE_FAIL,
				data: {},
			});
		}

	} catch (error) {
		console.log("exports.deleteStaff -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

exports.viewCompanyList = async (request, response) => {
	try {
		const companyDetail = await staffModel.viewCompanyList(request.body);
		if (companyDetail !== "") {
			response.status(SUCCESS_CODE).json({
				message: COMPANY_RETRIEVED_SUCCESS,
				data: companyDetail,
			});
		} else {
			response.status(EXPECTATION_FAILED_CODE).json({
				message: COMPANY_NOT_FOUND,
				data: {},
			});
		}
	} catch (error) {
		console.log("exports.viewCompanyList -> error: ", error);
		response.status(EXPECTATION_FAILED_CODE).json({
			message: COMPANY_NOT_FOUND,
			data: {},
		});
	}
};

exports.staffBasicController = async (request, response) => {
	try {
		const { staff_type } = request.body;
		request.query.search = request.query.search ? request.query.search : "";
		const staffModelBasicData = await staffModel.getStaffBasicData(staff_type, request.query);
		if (staffModelBasicData)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				STAFF_RETRIEVED_SUCCESS,
				staffModelBasicData
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				3,
				NOT_FOUND_CODE,
				{}
			);
	}
	catch (reason) {
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.staffListByShipmentController = async (request, response) => {
	try {
		const { shipmentId } = request.params;
		const { company_id } = request.body;
		const staffListByShipment = await staffModel.getStaffListWithJobAssignedData(shipmentId, company_id);
		if (staffListByShipment)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				STAFF_RETRIEVED_SUCCESS,
				staffListByShipment
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				STAFF_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.staffListByShipmentCompanyController = async (request, response) => {
	try {

		const { shipmentId } = request.params;
		const { company_id } = request.body;

		const staffListByShipment = await staffModel.getStaffListWithJobAssignedCompanyData(shipmentId, company_id);
		if (staffListByShipment)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				STAFF_RETRIEVED_SUCCESS,
				staffListByShipment
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				STAFF_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.hasValidStaffController = async (request, response, next) => {
	if (await staffModel.isStaffModel(request.body.sales_represent)) {
		next();
	} else {
		commonFunction.generateResponse(
			response,
			NOT_FOUND_CODE,
			0,
			SALES_NOT_FOUND,
			{}
		);
	}
};

exports.isValidStaffIdController = async (request, response, next) => {
	if (await staffModel.isStaffModel(request.body.staff_id)) {
		next();
	} else {
		commonFunction.generateResponse(
			response,
			NOT_FOUND_CODE,
			0,
			STAFF_NOT_FOUND,
			{}
		);
	}
};

exports.staffPasswordJson = async (request, response, storage_staff_id, consumerLoginJson) => {
	try {
		const editPassword = JSON.stringify({
			userId: storage_staff_id,
			newPassword: request.body.password,
		});
		const editPasswordResponse = await axios.post(`${MOVER_STORAGE_API_URL}import/change-password`, editPassword,
			{
				headers: {
					'Content-Type': 'application/json',
					'accessToken': consumerLoginJson.data.accessToken,
				}
			})

		if (editPasswordResponse.data !== "" && editPasswordResponse.data !== undefined && editPasswordResponse.data !== null) {
			return editPasswordResponse.data;
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Password update error 2", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Password update error 2", {});
	}
}

exports.consumerLoginJsonFun = async (request, response, getCompanyDetails) => {
	const consumerLoginJson = JSON.stringify({
		companyIdTokenMoverInventory: getCompanyDetails.integration_key,
		email: "<EMAIL>",
		password: "5PLaRAqq",
		deviceToken: "abcd",
		deviceType: 0,
	});
	try {
		const consumerLoginResponse = await axios.post(`${MOVER_STORAGE_API_URL}login`, consumerLoginJson, {
			headers: {
				'Content-Type': 'application/json'
			}
		})
		if (consumerLoginResponse.data !== "" && consumerLoginResponse.data !== undefined && consumerLoginJson.data !== null) {
			return consumerLoginResponse.data;
		}
		else {
			commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {});
		}

	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Consumer Login Fail", {});
	}
}

exports.updatePassword = async (request, response) => {
	try {
		const staffDetail = await staffModel.updatePassword(request.body);

		const staffData = await staffModel.viewStaff(
			request.company_id,
			request.body.staff_id
		);

		const findCompanyByEmail = await staffModel.findCompanyByEmail(staffData.email);

		if (findCompanyByEmail) {
			const companyDetail = await staffModel.updatePasswordCompany(request.body, staffData.email);
		}

		if (staffData !== "") {
			let html = await commonFunction.readFile("welcome.html");
			html = html.replace(/{{name}}/g, staffData.first_name);
			html = html.replace(/{{email}}/g, staffData.email);
			html = html.replace(/{{password}}/g, request.body.password);
			html = html.replace(/{{cmsUrl}}/g, CMS_URL_LINK);

			await commonFunction.sendEmail(
				staffData.email,
				"Password Change",
				html
			);
		}
		response.status(SUCCESS_CODE).json({
			status: 1,
			message: STAFF_PASSWORD_UPDATED,
			data: {},
		});
	} catch (error) {
		console.log("exports.updatePassword -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};

// exports.checkStaffAssignToJob = async (request, response, next) => {
// 	const { count } = await staffModel.checkStaffAssignToJob(
// 		request.body.staff_id
// 	);
// 	if (count && count > 0) {
// 		commonFunction.generateResponse(
// 			response,
// 			EXPECTATION_FAILED_CODE,
// 			0,
// 			NOT_DELETE,
// 			{}
// 		);
// 	} else {
// 		next();
// 	}
// };


exports.checkStaffAssignToJob = async (request, response, next) => {
	try {
		const { count } = await staffModel.checkStaffAssignToJob(
			request.body.staff_id
		);
		if (count && count > 0) {
			commonFunction.generateResponse(
				response,
				NO_CONTENT_CODE,
				0,
				{}
			);
		} else {
			commonFunction.generateResponse(
				response,
				NO_CONTENT_CODE,
				1,
				{}
			);
		}
	} catch (error) {
		console.log("exports.checkStaffAssignToJob -> error: ", error);
		response.status(SERVER_ERROR_CODE).json({
			status: 0,
			message: error.message,
			data: {},
		});
	}
};
