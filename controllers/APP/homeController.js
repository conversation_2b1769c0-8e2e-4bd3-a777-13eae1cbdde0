const homeModel = require("../../models/APP/homeModel");
const staffModel = require("../../models/APP/staffModel");
const companyModel = require("../../models/Admin/companyModel");
const commonModel = require("../../models/Admin/commonModel");
const qrCodeModel = require("../../models/Admin/qrCodeModel");
const shipmentModel = require("../../models/Admin/shipmentModel");
const commonFunction = require("../../assets/common");
const ActionLogModel = require("../../models/Admin/ActionLogModel");
const bcrypt = require("bcrypt");
const AWS = require("aws-sdk");
const fs = require("fs");
const sharp = require("sharp");
const { info } = require("console");
const path = require("path");
const axios = require("axios");
const FormData = require("form-data");
const { findIndex } = require("lodash");

exports.itemsHistoryCountByUnitsController = async (request, response) => {
  const { unitArray, shipmentId } = request.body;
  try {
    if (unitArray.length) {
      const multiData = [];
      for (let index = 0; index < unitArray.length; index++) {
        let Options = {};
        const itemsHistory = await homeModel.itemsHistoryCountByUnitsModel(
          unitArray[index],
          shipmentId
        );
        Options["shipment_job_id"] = shipmentId;
        Options["storage_unit_id"] = unitArray[index];
        Options["count"] = itemsHistory.count;
        multiData.push(Options);
      }
      response.status(SUCCESS_CODE).json({
        status: 1,
        message: "Data retrieved successfully!.",
        data: multiData,
      });
    } else {
      response.status(SUCCESS_CODE).json({
        status: 0,
        message: "data not found !",
        data: {},
      });
    }
  } catch (error) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.itemsHistoryCountByShipmentsController = async (request, response) => {
  const { shipmentArray, unitId } = request.body;
  try {
    if (shipmentArray.length) {
      const multiData = [];
      for (let index = 0; index < shipmentArray.length; index++) {
        let Options = {};
        const itemsHistory = await homeModel.itemsHistoryCountByShipmentsModel(
          shipmentArray[index],
          unitId
        );
        const fetchStorageIditemsHistory =
          await homeModel.fetchStorageIditemsHistory(shipmentArray[index]);
        Options["storage_unit_id"] = unitId;
        Options["shipment_job_id"] = shipmentArray[index];
        Options["storage_shipment_job_id"] =
          fetchStorageIditemsHistory.storage_shipment_job_id;
        Options["count"] = itemsHistory.count;
        multiData.push(Options);
      }
      response.status(SUCCESS_CODE).json({
        status: 1,
        message: "Data retrieved successfully!.",
        data: multiData,
      });
    } else {
      response.status(SUCCESS_CODE).json({
        status: 0,
        message: "data not found !",
        data: {},
      });
    }
  } catch (error) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.itemsHistoryByArrayController = async (request, response) => {
  const { unitArray, shipmentArray } = request.body;
  try {
    if (unitArray.length > 0 && shipmentArray.length > 0) {
      request.query.search = request.query.search ? request.query.search : "";
      const itemsHistory = await homeModel.itemsHistoryByArray(
        request.query,
        unitArray,
        shipmentArray
      );
      response.status(SUCCESS_CODE).json({
        status: 1,
        message: "Items retrieved successfully!.",
        data: itemsHistory,
      });
    } else {
      response.status(SUCCESS_CODE).json({
        status: 0,
        message: "data not found !",
        data: {},
      });
    }
  } catch (error) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.checkUnitValidationInventory = async (request, response, next) => {
  try {
    const checkAssignUnitAvailableInInventory =
      await homeModel.checkAssignUnitAvailableInInventory(request.body);
    if (checkAssignUnitAvailableInInventory.status == "empty") {
      response.status(SUCCESS_CODE).json({
        status: 1,
        message: "Unit available,",
        data: true,
      });
    } else {
      if (
        checkAssignUnitAvailableInInventory.shipment_job_id ===
        Number(request.body.shipmentId)
      ) {
        response.status(SUCCESS_CODE).json({
          status: 1,
          message: "Unit available.",
          data: true,
        });
      } else {
        response.status(SERVER_ERROR_CODE).json({
          status: 0,
          message: "Unit assign to another shipment.",
          data: false,
        });
      }
    }
  } catch (error) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.consumerLoginJsonFun = async (request, response, getCompanyDetails) => {
  const consumerLoginJson = JSON.stringify({
    companyIdTokenMoverInventory: getCompanyDetails.integration_key,
    email: "<EMAIL>",
    password: "5PLaRAqq",
    deviceToken: "abcd",
    deviceType: 0,
  });
  try {
    const consumerLoginResponse = await axios.post(
      `${MOVER_STORAGE_API_URL}login`,
      consumerLoginJson,
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    if (
      consumerLoginResponse.data !== "" &&
      consumerLoginResponse.data !== undefined &&
      consumerLoginResponse.data !== null
    ) {
      return consumerLoginResponse.data;
    } else {
      commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        "Consumer Login Fail",
        {}
      );
    }
  } catch (error) {
    commonFunction.generateResponse(
      response,
      SERVER_ERROR_CODE,
      0,
      "Consumer Login Fail",
      {}
    );
  }
};

exports.deleteItemStorageJson = async (
  request,
  response,
  inventory_id,
  jobData,
  isLastItem,
  consumerLoginJson
) => {
  const deleteItemStorageJson = JSON.stringify({
    shipment_inventory_id: inventory_id,
    storage_unit_id: jobData.storage_unit_id,
    shipment_job_id: jobData.shipment_job_id,
    lastItem: isLastItem,
  });
  try {
    const deleteItemStorageResponse = await axios.post(
      `${MOVER_STORAGE_API_URL}import/mover-inventory/delete-item`,
      deleteItemStorageJson,
      {
        headers: {
          "Content-Type": "application/json",
          accessToken: consumerLoginJson.data.accessToken,
        },
      }
    );
    if (
      deleteItemStorageResponse.data !== "" &&
      deleteItemStorageResponse.data !== undefined &&
      deleteItemStorageResponse.data !== null
    ) {
      return deleteItemStorageResponse.data;
    } else {
      commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        "Item delete Fail",
        {}
      );
    }
  } catch (error) {
    commonFunction.generateResponse(
      response,
      SERVER_ERROR_CODE,
      0,
      "Item delete Fail",
      {}
    );
  }
};

exports.updateBulkItemUnitStorageJson = async (
  request,
  response,
  findOldUnitOfItem,
  isLastItem,
  consumerLoginJson
) => {
  const updateItemUnitStorageJson = JSON.stringify({
    shipment_inventory_id: findOldUnitOfItem.shipment_inventory_id,
    old_storage_unit_id: findOldUnitOfItem.storage_unit_id,
    new_storage_unit_id: request.body.storage_unit_id,
    shipment_job_id: request.body.job_id,
    lastItem: isLastItem,
  });
  try {
    const updateItemUnitStorageResponse = await axios.post(
      `${MOVER_STORAGE_API_URL}import/mover-inventory/edit-item`,
      updateItemUnitStorageJson,
      {
        headers: {
          "Content-Type": "application/json",
          accessToken: consumerLoginJson.data.accessToken,
        },
      }
    );
    if (
      updateItemUnitStorageResponse.data !== "" &&
      updateItemUnitStorageResponse.data !== undefined &&
      updateItemUnitStorageResponse.data !== null
    ) {
      return updateItemUnitStorageResponse.data;
    } else {
      commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        "Item update Fail",
        {}
      );
    }
  } catch (error) {
    commonFunction.generateResponse(
      response,
      SERVER_ERROR_CODE,
      0,
      "Item update Fail",
      {}
    );
  }
};

exports.updateItemUnitStorageJson = async (
  request,
  response,
  findOldUnitOfItem,
  isLastItem,
  consumerLoginJson
) => {
  const updateItemUnitStorageJson = JSON.stringify({
    shipment_inventory_id: request.body.inventory_id,
    old_storage_unit_id: findOldUnitOfItem.storage_unit_id,
    new_storage_unit_id: request.body.storage_unit_id,
    shipment_job_id: request.body.job_id,
    lastItem: isLastItem,
  });
  try {
    const updateItemUnitStorageResponse = await axios.post(
      `${MOVER_STORAGE_API_URL}import/mover-inventory/edit-item`,
      updateItemUnitStorageJson,
      {
        headers: {
          "Content-Type": "application/json",
          accessToken: consumerLoginJson.data.accessToken,
        },
      }
    );
    if (
      updateItemUnitStorageResponse.data !== "" &&
      updateItemUnitStorageResponse.data !== undefined &&
      updateItemUnitStorageResponse.data !== null
    ) {
      return updateItemUnitStorageResponse.data;
    } else {
      commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        "Item update Fail",
        {}
      );
    }
  } catch (error) {
    commonFunction.generateResponse(
      response,
      SERVER_ERROR_CODE,
      0,
      "Item update Fail",
      {}
    );
  }
};

exports.warehouseManJsonFun = async (
  request,
  response,
  getStaffCompanyId,
  fetchWarehouseManDetails,
  consumerLoginJson
) => {
  function formatDate(dateVal) {
    let newDate = new Date(dateVal);

    let sMonth = padValue(newDate.getMonth() + 1);
    let sDay = padValue(newDate.getDate());
    let sYear = newDate.getFullYear();
    let sHour = newDate.getHours();
    let sMinute = padValue(newDate.getMinutes());
    let sAMPM = "AM";

    let iHourCheck = parseInt(sHour);

    if (iHourCheck > 12) {
      sAMPM = "PM";
      sHour = iHourCheck - 12;
    } else if (iHourCheck === 0) {
      sHour = "12";
    }

    sHour = padValue(sHour);

    return (
      sMonth +
      "-" +
      sDay +
      "-" +
      sYear +
      " " +
      ":" +
      " " +
      sHour +
      ":" +
      sMinute +
      " " +
      sAMPM
    );
  }

  function padValue(value) {
    return value < 10 ? "0" + value : value;
  }

  const warehouseManJson = JSON.stringify({
    shipmentId: fetchWarehouseManDetails.shipment_job_id,
    userId: "",
    warehousemanNotes: fetchWarehouseManDetails.warehouse_man_notes,
    wareHouseManName: fetchWarehouseManDetails.warehouse_man_name,
    datetime: formatDate(new Date()),
    signatureImageUrl: fetchWarehouseManDetails.dataValues.profile_photo,
  });

  try {
    //stageCompleteChangesApis
    const warehouseManResponse = await axios.post(
      `${MOVER_STORAGE_API_URL}import/mover-inventory/save-warehouseman-detail`,
      warehouseManJson,
      {
        headers: {
          "Content-Type": "application/json",
          accessToken: consumerLoginJson.data.accessToken,
        },
      }
    );
    if (
      warehouseManResponse.data !== "" &&
      warehouseManResponse.data !== undefined &&
      warehouseManResponse.data !== null
    ) {
      return warehouseManResponse.data;
    } else {
      commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        "WarehouseMan data found.",
        {}
      );
    }
  } catch (error) {
    commonFunction.generateResponse(
      response,
      SERVER_ERROR_CODE,
      0,
      "Data not found.",
      {}
    );
  }
};

exports.checkAssignUnitAvailableInStorage = async (
  request,
  response,
  consumerLoginJson
) => {
  try {
    const unitAvailableResponse = await axios.get(
      `${MOVER_STORAGE_API_URL}import/mover-inventory/check-unit-availability/${request.body.storage_unit_id}`,
      {
        headers: {
          "Content-Type": "application/json",
          accessToken: consumerLoginJson.data.accessToken,
        },
      }
    );
    if (
      unitAvailableResponse.data !== "" &&
      unitAvailableResponse.data !== undefined &&
      unitAvailableResponse.data !== null
    ) {
      return unitAvailableResponse.data;
    } else {
      commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        "Unit not found.",
        {}
      );
    }
  } catch (error) {
    commonFunction.generateResponse(
      response,
      SERVER_ERROR_CODE,
      0,
      "Unit not found.",
      {}
    );
  }
};

exports.editAssignUnitAvailableInStorage = async (
  request,
  response,
  consumerLoginJson
) => {
  try {
    const editAvailableJson = JSON.stringify({
      shipmentId: request.body.job_id,
      unitId: request.body.storage_unit_id,
    });

    const editAvailableResponse = await axios.post(
      `${MOVER_STORAGE_API_URL}import/mover-inventory/update-shipment-status`,
      editAvailableJson,
      {
        headers: {
          "Content-Type": "application/json",
          accessToken: consumerLoginJson.data.accessToken,
        },
      }
    );

    if (
      editAvailableResponse.data !== "" &&
      editAvailableResponse.data !== undefined &&
      editAvailableResponse.data !== null
    ) {
      return editAvailableResponse.data;
    } else {
      commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        "Unit update error",
        {}
      );
    }
  } catch (error) {
    commonFunction.generateResponse(
      response,
      SERVER_ERROR_CODE,
      0,
      "Unit update error",
      {}
    );
  }
};

exports.checkAssignUnitValidationStorageAdd = async (
  request,
  response,
  next
) => {
  try {
    if (
      request.body.storage_unit_id !== null &&
      request.body.storage_unit_id !== undefined &&
      request.body.storage_unit_id !== ""
    ) {
      const checkAssignUnitAvailableInInventory =
        await homeModel.checkAssignUnitAvailableInInventory(request.body);

      if (checkAssignUnitAvailableInInventory.status == "empty") {
        let headerData = await commonFunction.jwtTokenDecode(
          request.headers.access_token
        );
        let getStaffCompanyId = await staffModel.getStaffCompanyId(
          headerData.payload.user_id
        );
        const getCompanyDetails = await companyModel.getIntegrationKeyData(
          getStaffCompanyId
        );

        // consumerLoginJson
        const consumerLoginJson = await this.consumerLoginJsonFun(
          request,
          response,
          getCompanyDetails
        );

        //checkAssignUnitAvailableInStorage
        const checkAssignUnitAvailableInStorage =
          await this.checkAssignUnitAvailableInStorage(
            request,
            response,
            consumerLoginJson
          );

        if (checkAssignUnitAvailableInStorage.data == true) {
          const editAssignUnitAvailableInStorage =
            await this.editAssignUnitAvailableInStorage(
              request,
              response,
              consumerLoginJson
            );

          if (editAssignUnitAvailableInStorage.status === 200) {
            const assignUnitToShipment = await homeModel.assignUnitToShipment(
              request.body
            );
            next();
          } else {
            response.status(SERVER_ERROR_CODE).json({
              status: 0,
              message: "Unit assign error storage.",
              data: {},
            });
          }
        } else {
          response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: "Unit assign to another shipment.",
            data: checkAssignUnitAvailableInStorage.data,
          });
        }
      } else {
        if (
          checkAssignUnitAvailableInInventory.shipment_job_id ===
          Number(request.body.job_id)
        ) {
          next();
        } else {
          response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: "Unit assign to another shipment.",
            data: {},
          });
        }
      }
    } else {
      next();
    }
  } catch (error) {
    console.log("exports.checkAssignUnitValidationStorage -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error,
      data: {},
    });
  }
};

exports.checkAssignUnitValidationStorageEdit = async (
  request,
  response,
  next
) => {
  try {
    const checkIsCmsEdit =
      request &&
        request.body.itemEditByCms &&
        (request.body.itemEditByCms == true ||
          request.body.itemEditByCms == "true")
        ? true
        : false;
    if (
      request.body.storage_unit_id !== String(null) &&
      request.body.storage_unit_id !== undefined &&
      request.body.storage_unit_id !== ""
    ) {
      const checkAssignUnitAvailableInInventory =
        await homeModel.checkAssignUnitAvailableInInventory(request.body);

      if (checkAssignUnitAvailableInInventory.status == "empty") {
        if (checkIsCmsEdit) {
          const getCompanyDetails = await companyModel.getIntegrationKeyData(
            request.body
          );
          // consumerLoginJson
          const consumerLoginJson = await this.consumerLoginJsonFun(
            request,
            response,
            getCompanyDetails
          );
          //checkAssignUnitAvailableInStorage
          const checkAssignUnitAvailableInStorage =
            await this.checkAssignUnitAvailableInStorage(
              request,
              response,
              consumerLoginJson
            );

          if (checkAssignUnitAvailableInStorage.data == true) {
            const editAssignUnitAvailableInStorage =
              await this.editAssignUnitAvailableInStorage(
                request,
                response,
                consumerLoginJson
              );

            if (editAssignUnitAvailableInStorage.status === 200) {
              const assignUnitToShipment = await homeModel.assignUnitToShipment(
                request.body
              );
              //checksameunitornot
              const findOldUnitOfItem = await homeModel.findOldUnitOfItem(
                request.body
              );

              if (
                findOldUnitOfItem.storage_unit_id !== null &&
                findOldUnitOfItem.storage_unit_id !== "" &&
                findOldUnitOfItem.storage_unit_id !== undefined
              ) {
                const allUnitItemsWithJob = await homeModel.allUnitItemsWithJob(
                  findOldUnitOfItem
                );
                const editItemHistoryStorgae =
                  await homeModel.editItemHistoryStorgae(request.body);
                const updateItemUnitStorageJson =
                  await this.updateItemUnitStorageJson(
                    request,
                    response,
                    findOldUnitOfItem,
                    allUnitItemsWithJob.count === 1,
                    consumerLoginJson
                  );

                if (allUnitItemsWithJob.count === 1) {
                  const emptyUnitUpdateById =
                    await homeModel.emptyUnitUpdateById(
                      findOldUnitOfItem.storage_unit_id
                    );
                }
                next();
              } else {
                const addItemHistoryStorgae =
                  await homeModel.addItemHistoryStorgae(
                    request.body,
                    findOldUnitOfItem
                  );
                next();
              }
            } else {
              response.status(SERVER_ERROR_CODE).json({
                status: 0,
                message: "Unit assign error storage.",
                data: {},
              });
            }
          } else {
            response.status(SERVER_ERROR_CODE).json({
              status: 0,
              message: "Unit assign to another shipment.",
              data: checkAssignUnitAvailableInStorage.data,
            });
          }
        } else {
          let headerData = await commonFunction.jwtTokenDecode(
            request.headers.access_token
          );
          let getStaffCompanyId = await staffModel.getStaffCompanyId(
            headerData.payload.user_id
          );
          const getCompanyDetails = await companyModel.getIntegrationKeyData(
            getStaffCompanyId
          );

          // consumerLoginJson
          const consumerLoginJson = await this.consumerLoginJsonFun(
            request,
            response,
            getCompanyDetails
          );

          //checkAssignUnitAvailableInStorage
          const checkAssignUnitAvailableInStorage =
            await this.checkAssignUnitAvailableInStorage(
              request,
              response,
              consumerLoginJson
            );

          if (checkAssignUnitAvailableInStorage.data == true) {
            const editAssignUnitAvailableInStorage =
              await this.editAssignUnitAvailableInStorage(
                request,
                response,
                consumerLoginJson
              );

            if (editAssignUnitAvailableInStorage.status === 200) {
              const assignUnitToShipment = await homeModel.assignUnitToShipment(
                request.body
              );
              //checksameunitornot
              const findOldUnitOfItem = await homeModel.findOldUnitOfItem(
                request.body
              );

              if (
                findOldUnitOfItem.storage_unit_id !== null &&
                findOldUnitOfItem.storage_unit_id !== "" &&
                findOldUnitOfItem.storage_unit_id !== undefined
              ) {
                const allUnitItemsWithJob = await homeModel.allUnitItemsWithJob(
                  findOldUnitOfItem
                );
                const editItemHistoryStorgae =
                  await homeModel.editItemHistoryStorgae(request.body);
                const updateItemUnitStorageJson =
                  await this.updateItemUnitStorageJson(
                    request,
                    response,
                    findOldUnitOfItem,
                    allUnitItemsWithJob.count === 1,
                    consumerLoginJson
                  );

                if (allUnitItemsWithJob.count === 1) {
                  const emptyUnitUpdateById =
                    await homeModel.emptyUnitUpdateById(
                      findOldUnitOfItem.storage_unit_id
                    );
                }
                next();
              } else {
                const addItemHistoryStorgae =
                  await homeModel.addItemHistoryStorgae(
                    request.body,
                    findOldUnitOfItem
                  );
                next();
              }
            } else {
              response.status(SERVER_ERROR_CODE).json({
                status: 0,
                message: "Unit assign error storage.",
                data: {},
              });
            }
          } else {
            response.status(SERVER_ERROR_CODE).json({
              status: 0,
              message: "Unit assign to another shipment.",
              data: checkAssignUnitAvailableInStorage.data,
            });
          }
        }
      } else {
        if (
          checkAssignUnitAvailableInInventory.shipment_job_id ===
          Number(request.body.job_id)
        ) {
          //checksameunitornot
          const findOldUnitOfItem = await homeModel.findOldUnitOfItem(
            request.body
          );

          if (
            findOldUnitOfItem.storage_unit_id !== null &&
            findOldUnitOfItem.storage_unit_id !== "" &&
            findOldUnitOfItem.storage_unit_id !== undefined
          ) {
            const allUnitItemsWithJob = await homeModel.allUnitItemsWithJob(
              findOldUnitOfItem
            );

            if (allUnitItemsWithJob.count === 1) {
              let emptyUnitUpdateById = homeModel.emptyUnitUpdateById(
                findOldUnitOfItem.storage_unit_id
              );
            }

            const editItemHistoryStorgae =
              await homeModel.editItemHistoryStorgae(request.body);
            let getCompanyDetails, consumerLoginJson;

            if (checkIsCmsEdit) {
              getCompanyDetails = await companyModel.getIntegrationKeyData(
                request.body
              );
            } else {
              let headerData = await commonFunction.jwtTokenDecode(
                request.headers.access_token
              );
              let getStaffCompanyId = await staffModel.getStaffCompanyId(
                headerData.payload.user_id
              );
              getCompanyDetails = await companyModel.getIntegrationKeyData(
                getStaffCompanyId
              );
            }

            consumerLoginJson = await this.consumerLoginJsonFun(
              request,
              response,
              getCompanyDetails
            );
            const updateItemUnitStorageJson =
              await this.updateItemUnitStorageJson(
                request,
                response,
                findOldUnitOfItem,
                allUnitItemsWithJob.count === 1,
                consumerLoginJson
              );
            next();
          } else {
            const addItemHistoryStorgae = await homeModel.addItemHistoryStorgae(
              request.body,
              findOldUnitOfItem
            );
            next();
          }
        } else {
          response.status(SERVER_ERROR_CODE).json({
            message: "Unit assign to another shipment.",
            data: {},
          });
        }
      }
    } else {
      next();
    }
  } catch (error) {
    console.log("exports.checkAssignUnitValidationStorage -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error,
      data: {},
    });
  }
};

exports.checkEditItemUnitValidation = async (request, response, next) => {
  try {
    if (
      request.body.storage_unit_id !== null &&
      request.body.storage_unit_id !== undefined &&
      request.body.storage_unit_id !== ""
    ) {
      const checkAssignUnitAvailableInInventory =
        await homeModel.checkAssignUnitAvailableInInventory(request.body);
      let headerData = await commonFunction.jwtTokenDecode(
        request.headers.access_token
      );
      let getStaffCompanyId = await staffModel.getStaffCompanyId(
        headerData.payload.user_id
      );
      const getCompanyDetails = await companyModel.getIntegrationKeyData(
        getStaffCompanyId
      );

      // consumerLoginJson
      const consumerLoginJson = await this.consumerLoginJsonFun(
        request,
        response,
        getCompanyDetails
      );

      if (checkAssignUnitAvailableInInventory.status == "empty") {
        //checkAssignUnitAvailableInStorage
        const checkAssignUnitAvailableInStorage =
          await this.checkAssignUnitAvailableInStorage(
            request,
            response,
            consumerLoginJson
          );

        if (checkAssignUnitAvailableInStorage.data == true) {
          const editAssignUnitAvailableInStorage =
            await this.editAssignUnitAvailableInStorage(
              request,
              response,
              consumerLoginJson
            );

          if (editAssignUnitAvailableInStorage.status === 200) {
            const assignUnitToShipment = await homeModel.assignUnitToShipment(
              request.body
            );
            //checksameunitornot
            const findOldUnitOfItem = await homeModel.findOldUnitOfItem(
              request.body
            );

            if (
              findOldUnitOfItem.storage_unit_id !== null &&
              findOldUnitOfItem.storage_unit_id !== "" &&
              findOldUnitOfItem.storage_unit_id !== undefined
            ) {
              request.body.isItemAssignToOldUnit = true;
              request.body.oldUnitName =
                findOldUnitOfItem &&
                findOldUnitOfItem.unit_list &&
                findOldUnitOfItem.unit_list.name;

              const allUnitItemsWithJob = await homeModel.allUnitItemsWithJob(
                findOldUnitOfItem
              );
              const editItemHistoryStorgae =
                await homeModel.editItemHistoryStorgae(request.body);
              const updateItemUnitStorageJson =
                await this.updateItemUnitStorageJson(
                  request,
                  response,
                  findOldUnitOfItem,
                  allUnitItemsWithJob.count === 1,
                  consumerLoginJson
                );

              if (allUnitItemsWithJob.count === 1) {
                const emptyUnitUpdateById = await homeModel.emptyUnitUpdateById(
                  findOldUnitOfItem.storage_unit_id
                );
              }
              next();
            } else {
              request.body.isItemAssignToOldUnit = false;
              const addItemHistoryStorgae =
                await homeModel.addItemHistoryStorgae(
                  request.body,
                  findOldUnitOfItem
                );
              await homeModel.updateSingleTotalAddItemsToStorage(request.body);
              next();
            }
          } else {
            response.status(SERVER_ERROR_CODE).json({
              status: 0,
              message: "Unit assign error storage.",
              data: {},
            });
          }
        } else {
          response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: "Unit assign to another shipment.",
            data: checkAssignUnitAvailableInStorage.data,
          });
        }
      } else {
        if (
          checkAssignUnitAvailableInInventory.shipment_job_id ===
          Number(request.body.job_id)
        ) {
          //checksameunitornot
          const findOldUnitOfItem = await homeModel.findOldUnitOfItem(
            request.body
          );

          if (
            findOldUnitOfItem.storage_unit_id !== null &&
            findOldUnitOfItem.storage_unit_id !== "" &&
            findOldUnitOfItem.storage_unit_id !== undefined
          ) {
            request.body.isItemAssignToOldUnit = true;
            request.body.oldUnitName =
              findOldUnitOfItem &&
              findOldUnitOfItem.unit_list &&
              findOldUnitOfItem.unit_list.name;

            const allUnitItemsWithJob = await homeModel.allUnitItemsWithJob(
              findOldUnitOfItem
            );
            const editItemHistoryStorgae =
              await homeModel.editItemHistoryStorgae(request.body);
            const updateItemUnitStorageJson =
              await this.updateItemUnitStorageJson(
                request,
                response,
                findOldUnitOfItem,
                allUnitItemsWithJob.count === 1,
                consumerLoginJson
              );

            if (allUnitItemsWithJob.count === 1) {
              const emptyUnitUpdateById = await homeModel.emptyUnitUpdateById(
                findOldUnitOfItem.storage_unit_id
              );
            }
            next();
          } else {
            request.body.isItemAssignToOldUnit = false;
            const addItemHistoryStorgae = await homeModel.addItemHistoryStorgae(
              request.body,
              findOldUnitOfItem
            );
            await homeModel.updateSingleTotalAddItemsToStorage(request.body);
            next();
          }
        } else {
          response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: "Unit assign to another shipment.",
            data: {},
          });
        }
      }
    } else {
      next();
    }
  } catch (error) {
    commonFunction.generateResponse(
      response,
      SERVER_ERROR_CODE,
      0,
      "Unit validation error",
      {}
    );
  }
};

exports.checkBulkAssignItemUnitValidation = async (request, response, next) => {
  try {
    let getUserDetails = await commonModel.getUserDetails(request);
    if (
      request.body.storage_unit_id !== null &&
      request.body.storage_unit_id !== undefined &&
      request.body.storage_unit_id !== ""
    ) {
      const checkAssignUnitAvailableInInventory =
        await homeModel.checkAssignUnitAvailableInInventory(request.body);

      let headerData = await commonFunction.jwtTokenDecode(
        request.headers.access_token
      );
      let getStaffCompanyId = await staffModel.getStaffCompanyId(
        headerData.payload.user_id
      );
      const getCompanyDetails = await companyModel.getIntegrationKeyData(
        getStaffCompanyId
      );

      // consumerLoginJson
      const consumerLoginJson = await this.consumerLoginJsonFun(
        request,
        response,
        getCompanyDetails
      );
      if (checkAssignUnitAvailableInInventory.status == "empty") {
        //checkAssignUnitAvailableInStorage
        const checkAssignUnitAvailableInStorage =
          await this.checkAssignUnitAvailableInStorage(
            request,
            response,
            consumerLoginJson
          );

        if (checkAssignUnitAvailableInStorage.data == true) {
          const editAssignUnitAvailableInStorage =
            await this.editAssignUnitAvailableInStorage(
              request,
              response,
              consumerLoginJson
            );

          if (editAssignUnitAvailableInStorage.status === 200) {
            const assignUnitToShipment = await homeModel.assignUnitToShipment(
              request.body
            );
            //checksameunitornot
            let bulkItemData;

            if (
              request.body.isSelectAllItems == "true" ||
              request.body.isSelectAllItems == true
            ) {
              bulkItemData = await homeModel.findOldUnitOfBulkItemUsingShipment(
                request.body
              );
            } else {
              bulkItemData = await homeModel.findOldUnitOfBulkItem(
                request.body
              );
            }
            for (let i = 0; i < bulkItemData.length; i++) {
              const findOldUnitOfItem = bulkItemData[i];
              if (
                findOldUnitOfItem.storage_unit_id !== null &&
                findOldUnitOfItem.storage_unit_id !== "" &&
                findOldUnitOfItem.storage_unit_id !== undefined
              ) {
                const allUnitItemsWithJob = await homeModel.allUnitItemsWithJob(
                  findOldUnitOfItem
                );

                const itemDetail = await homeModel.editBulkItemUnit(
                  request.body,
                  findOldUnitOfItem
                );

                ActionLogModel.createActionLog({
                  platform: "APP",
                  performed_by_id: getUserDetails.staff_id,
                  performed_by_role: "User",
                  performed_by_name: getUserDetails.name,
                  action_type: "UNIT_ASSIGNED",
                  action_performed_on_id: findOldUnitOfItem.shipment_inventory_id,
                  action_performed_on_name: `Item - ${findOldUnitOfItem.item_name}, Unit - ${checkAssignUnitAvailableInInventory.name} `,
                })

                const editItemHistoryStorgae =
                  await homeModel.editBulkItemHistoryStorgae(
                    request.body,
                    findOldUnitOfItem
                  );
                const updateItemUnitStorageJson =
                  await this.updateBulkItemUnitStorageJson(
                    request,
                    response,
                    findOldUnitOfItem,
                    allUnitItemsWithJob.count == 1,
                    consumerLoginJson
                  );
                if (allUnitItemsWithJob.count == 1) {
                  const emptyUnitUpdateById =
                    await homeModel.emptyUnitUpdateById(
                      findOldUnitOfItem.storage_unit_id
                    );
                }
              } else {
                const itemDetail = await homeModel.editBulkItemUnit(
                  request.body,
                  findOldUnitOfItem
                );
                ActionLogModel.createActionLog({
                  platform: "APP",
                  performed_by_id: getUserDetails.staff_id,
                  performed_by_role: "User",
                  performed_by_name: getUserDetails.name,
                  action_type: "UNIT_ASSIGNED",
                  action_performed_on_id: findOldUnitOfItem.shipment_inventory_id,
                  action_performed_on_name: `Item - ${findOldUnitOfItem.item_name}, Unit - ${checkAssignUnitAvailableInInventory.name} `,
                })
                const addItemHistoryStorgae =
                  await homeModel.addBulkItemHistoryStorgae(
                    request.body,
                    findOldUnitOfItem
                  );
                await homeModel.updateSingleTotalAddItemsToStorage(
                  request.body
                );
              }
            }
            next();
          } else {
            response.status(SERVER_ERROR_CODE).json({
              status: 0,
              message: "Unit assign error storage.",
              data: {},
            });
          }
        } else {
          response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: "Unit assign to another shipment.",
            data: checkAssignUnitAvailableInStorage.data,
          });
        }
      } else {
        if (
          checkAssignUnitAvailableInInventory.shipment_job_id ===
          Number(request.body.job_id)
        ) {
          //checksameunitornot
          let bulkItemData;

          if (
            request.body.isSelectAllItems == "true" ||
            request.body.isSelectAllItems == true
          ) {
            bulkItemData = await homeModel.findOldUnitOfBulkItemUsingShipment(
              request.body
            );
          } else {
            bulkItemData = await homeModel.findOldUnitOfBulkItem(request.body);
          }

          for (let i = 0; i < bulkItemData.length; i++) {
            const findOldUnitOfItem = bulkItemData[i];

            if (
              findOldUnitOfItem.storage_unit_id !== null &&
              findOldUnitOfItem.storage_unit_id !== "" &&
              findOldUnitOfItem.storage_unit_id !== undefined
            ) {
              const findOldUnitOfItemResult =
                await homeModel.findOldUnitOfBulkAssignItem(findOldUnitOfItem);

              const allUnitItemsWithJob = await homeModel.allUnitItemsWithJob(
                findOldUnitOfItemResult
              );
              const itemDetail = await homeModel.editBulkItemUnit(
                request.body,
                findOldUnitOfItem
              );
              ActionLogModel.createActionLog({
                platform: "APP",
                performed_by_id: getUserDetails.staff_id,
                performed_by_role: "User",
                performed_by_name: getUserDetails.name,
                action_type: "UNIT_ASSIGNED",
                action_performed_on_id: findOldUnitOfItem.shipment_inventory_id,
                action_performed_on_name: `Item - ${findOldUnitOfItem.item_name}, Unit - ${checkAssignUnitAvailableInInventory.name} `,
              })
              const updateItemUnitStorageJson =
                await this.updateBulkItemUnitStorageJson(
                  request,
                  response,
                  findOldUnitOfItem,
                  allUnitItemsWithJob.count == 1,
                  consumerLoginJson
                );

              await homeModel.editBulkItemHistoryStorgae(
                request.body,
                findOldUnitOfItem
              );

              if (allUnitItemsWithJob.count == 1) {
                const emptyUnitUpdateById = await homeModel.emptyUnitUpdateById(
                  findOldUnitOfItem.storage_unit_id
                );
              }
            } else {
              const itemDetail = await homeModel.editBulkItemUnit(
                request.body,
                findOldUnitOfItem
              );
              ActionLogModel.createActionLog({
                platform: "APP",
                performed_by_id: getUserDetails.staff_id,
                performed_by_role: "User",
                performed_by_name: getUserDetails.name,
                action_type: "UNIT_ASSIGNED",
                action_performed_on_id: findOldUnitOfItem.shipment_inventory_id,
                action_performed_on_name: `Item - ${findOldUnitOfItem.item_name}, Unit - ${checkAssignUnitAvailableInInventory.name} `,
              })
              await homeModel.addBulkItemHistoryStorgae(
                request.body,
                findOldUnitOfItem
              );
              await homeModel.updateSingleTotalAddItemsToStorage(request.body);
            }
          }
          next();
        } else {
          response.status(SERVER_ERROR_CODE).json({
            status: 0,
            message: "Unit assign to another shipment.",
            data: {},
          });
        }
      }
    } else {
      next();
    }
  } catch (error) {
    console.log("error", error);
    commonFunction.generateResponse(
      response,
      SERVER_ERROR_CODE,
      0,
      "Unit validation error",
      {}
    );
  }
};

exports.addSignatureStorage = async (request, response, next) => {
  try {
    if (request.body.assign_storage_units_to_items == "yes") {
      let headerData = await commonFunction.jwtTokenDecode(
        request.headers.access_token
      );
      let getStaffCompanyId = await staffModel.getStaffCompanyId(
        headerData.payload.user_id
      );

      const getCompanyDetails = await companyModel.getIntegrationKeyData(
        getStaffCompanyId
      );
      const fetchWarehouseManDetails = await homeModel.fetchWarehouseManDetails(
        request.body
      );

      if (
        fetchWarehouseManDetails !== "" &&
        fetchWarehouseManDetails !== null &&
        fetchWarehouseManDetails !== undefined
      ) {
        // consumerLoginJson
        const consumerLoginJson = await this.consumerLoginJsonFun(
          request,
          response,
          getCompanyDetails
        );
        // warehouseManJson
        const warehouseManJson = await this.warehouseManJsonFun(
          request,
          response,
          getStaffCompanyId,
          fetchWarehouseManDetails,
          consumerLoginJson
        );
        if (warehouseManJson.status == 200) {
          next();
        } else {
          commonFunction.generateResponse(
            response,
            SERVER_ERROR_CODE,
            0,
            "Enter valid warehouse man details",
            {}
          );
        }
      } else {
        commonFunction.generateResponse(
          response,
          SERVER_ERROR_CODE,
          0,
          "Warehouse man deatils not found, please add.",
          {}
        );
      }
    } else {
      next();
    }
  } catch (error) {
    console.log("exports.addSignatureStorage -> error: ", error);
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.checkValidationWarehouseMan = async (request, response, next) => {
  try {
    const checkValidationWarehouseMan =
      await homeModel.checkValidationWarehouseMan(request.body);
    if (
      checkValidationWarehouseMan !== "" &&
      checkValidationWarehouseMan !== null &&
      checkValidationWarehouseMan !== undefined
    ) {
      commonFunction.generateResponse(
        response,
        SUCCESS_CODE,
        1,
        "Signature created already successfully!",
        {}
      );
    } else {
      next();
    }
  } catch (error) {
    console.log("exports.checkValidationWarehouseMan -> error: ", error);
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.addWarehouseMan = async (request, response, next) => {
  try {
    let newFileName = "";
    if (request.file && request.file.originalname !== "") {
      const fileName = request.file.filename;
      const fileExt = request.file.originalname.split(".");
      newFileName = fileName + "." + fileExt[1];
      const s3 = new AWS.S3();
      let params = {
        ACL: "public-read",
        Bucket: Const_AWS_BUCKET,
        Body: fs.createReadStream(request.file.path),
        Key: Const_AWS_Warehouse_Man_Profile + "original/" + newFileName,
      };
      let result = await commonFunction.UploadImageS3(params, s3);
      if (result) {
        fs.unlinkSync(request.file.path);
      }
    }
    const addWarehouseMan = await homeModel.addWarehouseMan(
      request.body,
      newFileName
    );
    let getStaffCompanyId = await staffModel.getStaffCompanyId(
      request.body.staffId
    );
    const getCompanyDetails = await companyModel.getIntegrationKeyData(
      getStaffCompanyId
    );
    const fetchWarehouseManDetails = await homeModel.fetchWarehouseManDetails(
      addWarehouseMan
    );

    if (
      fetchWarehouseManDetails !== "" &&
      fetchWarehouseManDetails !== null &&
      fetchWarehouseManDetails !== undefined
    ) {
      // consumerLoginJson
      const consumerLoginJson = await this.consumerLoginJsonFun(
        request,
        response,
        getCompanyDetails
      );
      // warehouseManJson
      const warehouseManJson = await this.warehouseManJsonFun(
        request,
        response,
        getStaffCompanyId,
        fetchWarehouseManDetails,
        consumerLoginJson
      );
      if (warehouseManJson.status == 200) {
        commonFunction.generateResponse(
          response,
          SUCCESS_CODE,
          1,
          "Signature created successfully!",
          {}
        );
      } else {
        commonFunction.generateResponse(
          response,
          SERVER_ERROR_CODE,
          0,
          "Enter valid warehouse man details",
          {}
        );
      }
    } else {
      commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        "Warehouse man deatils not found, please add.",
        {}
      );
    }
  } catch (error) {
    console.log("exports.addWarehouseMan -> error: ", error);
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.storageLogin = async (request, response) => {
  try {
    const storageLogin = await homeModel.storageLogin(request.body);
    const data = {};

    if (storageLogin && storageLogin !== "" && storageLogin !== null) {
      let passwordVerified = bcrypt.compareSync(
        request.body.password,
        storageLogin.password
      );

      if (passwordVerified === false) {
        commonFunction.generateResponse(
          response,
          EXPECTATION_FAILED_CODE,
          0,
          LOGIN_PASSWORD_FAIL,
          {}
        );
      } else {
        const tokenDetail = await commonModel.accesstokenStorage(
          storageLogin.staff_id,
          request.body
        );

        data["loginDetails"] = storageLogin;
        data["access_token"] = tokenDetail.access_token;

        commonFunction.generateResponse(
          response,
          SUCCESS_CODE,
          1,
          LOGIN_SUCCESS,
          data
        );
      }
    } else {
      commonFunction.generateResponse(
        response,
        EXPECTATION_FAILED_CODE,
        0,
        LOGIN_EMAIL_FAIL,
        {}
      );
    }
  } catch (error) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.checkQrCodeValidationController = async (request, response, next) => {
  try {
    const random_number = request.params.random_number
      ? request.params.random_number
      : request.body.random_number;

    const shipmentId = request.params.shipmentId
      ? request.params.shipmentId
      : request.body.shipment_id;

    const isValidQrCode = await homeModel.checkExistQrCodeModel(
      random_number.replace(/\s/g, "")
    );
    if (isValidQrCode) {
      const isValidQrCodeBelongToShipment =
        await homeModel.isValidQrCodeBelongToShipment(
          random_number.replace(/\s/g, ""),
          shipmentId
        );
      if (isValidQrCodeBelongToShipment) {
        next();
      } else {
        const getShipmentDetailsForGenericLabel =
          await homeModel.getShipmentDetailsForGenericLabel(shipmentId);
        const isValidQrCodeBelongToCompany =
          await homeModel.isValidQrCodeBelongToCompany(
            random_number.replace(/\s/g, ""),
            getShipmentDetailsForGenericLabel.company_id
          );

        if (isValidQrCodeBelongToCompany) {
          next();
        } else {
          commonFunction.generateResponse(
            response,
            NOT_FOUND_CODE,
            0,
            "This QR code is assigned to another shipment/company.",
            {}
          );
        }
      }
    } else {
      const data = {
        qr_generate_code: random_number.replace(/\s/g, ""),
        type: "External",
      };
      commonFunction.generateResponse(
        response,
        NO_CONTENT_CODE,
        1,
        "success",
        data
      );
    }
  } catch (error) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.isValidQrCodeStorageController = async (request, response, next) => {
  try {
    const random_number = request.params.random_number
      ? request.params.random_number
      : request.body.random_number;

    const isValidQrCode = await homeModel.checkExistQrCodeModel(
      random_number.replace(/\s/g, "")
    );
    if (isValidQrCode) {
      next();
    } else
      commonFunction.generateResponse(
        response,
        SUCCESS_CODE,
        0,
        QRCODE_NOT_FOUND,
        {}
      );
  } catch (error) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.isValidQrCodeController = async (request, response, next) => {
  try {
    const random_number = request.params.random_number
      ? request.params.random_number
      : request.body.random_number;

    const isValidQrCode = await homeModel.checkExistQrCodeModel(
      random_number.replace(/\s/g, "")
    );
    if (isValidQrCode) {
      next();
    } else
      commonFunction.generateResponse(
        response,
        NOT_FOUND_CODE,
        0,
        QRCODE_NOT_FOUND,
        {}
      );
  } catch (error) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.isQrCodeBelongToItemController = async (request, response, next) => {
  try {
    const random_number = request.params.random_number
      ? request.params.random_number
      : request.body.random_number;

    const getQrCodeDetialsForCheck = await homeModel.getQrCodeDetialsForCheck(
      random_number.replace(/\s/g, "")
    );
    if (getQrCodeDetialsForCheck) {
      const checkQrCodeBelongToItem = await homeModel.checkQrCodeBelongToItem(
        getQrCodeDetialsForCheck.qr_code_id
      );
      if (checkQrCodeBelongToItem) {
        commonFunction.generateResponse(
          response,
          NO_CONTENT_CODE,
          0,
          "Qr Code Already Assigned to Another Item",
          {}
        );
      } else {
        commonFunction.generateResponse(
          response,
          NO_CONTENT_CODE,
          1,
          "success",
          getQrCodeDetialsForCheck
        );
      }
    } else {
      commonFunction.generateResponse(
        response,
        NOT_FOUND_CODE,
        0,
        QRCODE_NOT_FOUND,
        {}
      );
    }
  } catch (error) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.getQrCodeDetailsController = async (request, response) => {
  try {
    const { random_number } = request.params;
    let homeData = {};
    let getUserDetails = await commonModel.getUserDetails(request);
    let getStaffCompanyId = await staffModel.getStaffCompanyId(
      getUserDetails.staff_id
    );
    let exceptionList = await homeModel.getExceptionList();
    let locationList = await homeModel.getLocationList();

    const randomNumberDetails = await homeModel.getQrCodeDetailsModel(
      random_number.replace(/\s/g, "")
    );
    if (randomNumberDetails) {
      let inventoryDetailsForQrcode = await homeModel.inventoryDetailsForQrcode(
        randomNumberDetails.qr_code_id,
        randomNumberDetails
      );
      let roomList = await homeModel.getRoomList(
        randomNumberDetails.shipment_job.company_id
      );

      homeData["qrcodedetails"] = randomNumberDetails;
      homeData["itemDetails"] = inventoryDetailsForQrcode.job_items[0];
      homeData["roomList"] = roomList;
      homeData["exceptionList"] = exceptionList;
      homeData["locationList"] = locationList;

      if (
        getStaffCompanyId.company_id ===
        randomNumberDetails.shipment_job.company_id
      ) {
        commonFunction.generateResponse(
          response,
          SUCCESS_CODE,
          1,
          QRDETAILS_DETAILS,
          homeData
        );
      } else {
        commonFunction.generateResponse(
          response,
          NO_CONTENT_CODE,
          0,
          "This item not belong to your company!.",
          {}
        );
      }
    } else
      commonFunction.generateResponse(
        response,
        SUCCESS_CODE,
        0,
        "Data not found!",
        {}
      );
  } catch (reason) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {});
  }
};

exports.getQrCodeDetailsForStorageController = async (request, response) => {
  try {
    const { random_number } = request.params;
    let homeData = {};
    const randomNumberData = await homeModel.getQrCodeDetailForStorageModel(
      random_number.replace(/\s/g, "")
    );
    if (randomNumberData) {
      let inventoryDetails = await homeModel.inventoryDetailsForStorageQrcode(
        randomNumberData
      );
      if (inventoryDetails !== null) {
        homeData["qrDetails"] = randomNumberData;
        homeData["itemDetails"] = inventoryDetails;

        commonFunction.generateResponse(
          response,
          SUCCESS_CODE,
          1,
          "Data retrieved successfully!.",
          homeData
        );
      } else {
        commonFunction.generateResponse(
          response,
          SUCCESS_CODE,
          0,
          "Item details not found!.",
          {}
        );
      }
    } else {
      commonFunction.generateResponse(
        response,
        SUCCESS_CODE,
        0,
        "Data not found!",
        {}
      );
    }
  } catch (error) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.getJobListByName = async (request, response) => {
  try {
    request.query.search = request.query.search ? request.query.search : "";
    let headerData = await commonFunction.jwtTokenDecode(
      request.headers.access_token
    );
    let jobList = await homeModel.getJobListByName(
      request.body,
      headerData.payload.user_id,
      request.query
    );
    if (jobList !== "") {
      response.status(SUCCESS_CODE).json({
        status: 1,
        message: JOB_RETRIEVED_SUCCESS,
        data: jobList,
      });
    } else {
      response.status(EXPECTATION_FAILED_CODE).json({
        status: 0,
        message: JOB_NOT_FOUND,
        data: {},
      });
    }
  } catch (error) {
    response.status(EXPECTATION_FAILED_CODE).json({
      status: 0,
      message: JOB_NOT_FOUND,
      data: {},
    });
  }
};

exports.getJobExceptionList = async (request, response) => {
  try {
    let getUserDetails = await commonModel.getUserDetails(request);
    let compnayDetails = await commonModel.getCompnayDeatils(
      getUserDetails.staff_id
    );
    let homeData = {};
    let jobList = await homeModel.getJobList(
      request.body,
      request.query,
      headerData.payload.user_id
    );
    let roomList = await homeModel.getRoomList(compnayDetails.company_id);
    let exceptionList = await homeModel.getExceptionList();
    let locationList = await homeModel.getLocationList();
    let genericQr = await qrCodeModel.genericQrListForJobList(
      compnayDetails.company_id
    );
    homeData["roomList"] = roomList;
    homeData["exceptionList"] = exceptionList;
    homeData["locationList"] = locationList;
    homeData["genericQr"] = genericQr;

    if (roomList !== "") {
      response.status(SUCCESS_CODE).json({
        status: 1,
        message: EXCEPTION_RETRIEVED_SUCCESS,
        data: homeData,
      });
    } else {
      response.status(EXPECTATION_FAILED_CODE).json({
        status: 0,
        message: EXCEPTION_NOT_FOUND,
        data: {},
      });
    }
  } catch (error) {
    response.status(EXPECTATION_FAILED_CODE).json({
      status: 0,
      message: EXCEPTION_NOT_FOUND,
      data: {},
    });
  }
};

exports.unitListEditItemController = async (request, response) => {
  try {
    const { jobId } = request.params;
    const fetchShipmentWarehouse = await shipmentModel.fetchShipmentWarehouse(
      jobId
    );
    let unitList = await homeModel.unitListEditItem(fetchShipmentWarehouse);
    response.status(SUCCESS_CODE).json({
      status: 1,
      message: UNIT_RETRIEVED_SUCCESS,
      data: unitList,
    });
  } catch (error) {
    response.status(EXPECTATION_FAILED_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.jobItemRoomListController = async (request, response) => {
  try {
    const shipmentId = request.body.shipmentId;
    const responseData = await homeModel.jobItemRoomListModel(shipmentId);
    const sortRoomArray = await responseData.sort(function (a, b) {
      const nameA = a.room_name.toUpperCase();
      const nameB = b.room_name.toUpperCase();
      if (nameA > nameB) {
        return 1;
      }
      if (nameA < nameB) {
        return -1;
      }
      return 0;
    });
    response.status(SUCCESS_CODE).json({
      status: 1,
      message: ROOM_RETRIEVED_SUCCESS,
      data: sortRoomArray,
    });
  } catch (error) {
    console.log("exports.jobItemRoomListController -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.jobItemTagListController = async (request, response) => {
  try {
    const shipmentId = request.body.shipmentId;
    const responseData = await homeModel.jobItemTagListModel(shipmentId);
    const sortTagArray = await responseData.sort(function (a, b) {
      const nameA = a.dataValues
        ? a.dataValues.name.toUpperCase()
        : a.name.toUpperCase();
      const nameB = b.dataValues
        ? b.dataValues.name.toUpperCase()
        : b.name.toUpperCase();
      if (nameA > nameB) {
        return 1;
      }
      if (nameA < nameB) {
        return -1;
      }
      return 0;
    });
    response.status(SUCCESS_CODE).json({
      status: 1,
      message: TAG_RETRIEVED_SUCCESS,
      data: sortTagArray,
    });
  } catch (error) {
    console.log("exports.jobItemTagListController -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.roomListEditItemController = async (request, response) => {
  try {
    let getUserDetails = await commonModel.getUserDetails(request);
    let roomList = await homeModel.roomListEditItem(getUserDetails);
    response.status(SUCCESS_CODE).json({
      status: 1,
      message: ROOM_RETRIEVED_SUCCESS,
      data: roomList,
    });
  } catch (error) {
    response.status(EXPECTATION_FAILED_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.userListEditItemController = async (request, response) => {
  try {
    let getUserDetails = await commonModel.getUserDetails(request);
    let userList = await homeModel.userListEditItem(getUserDetails);
    response.status(SUCCESS_CODE).json({
      status: 1,
      message: USER_RETRIEVED_SUCCESS,
      data: userList,
    });
  } catch (error) {
    response.status(EXPECTATION_FAILED_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.exceptionListController = async (request, response) => {
  try {
    request.query.search = request.query.search ? request.query.search : "";
    let headerData = await commonFunction.jwtTokenDecode(
      request.headers.access_token
    );
    let getUserDetails = await commonModel.getUserDetails(request);
    let compnayDetails = await commonModel.getCompnayDeatils(
      getUserDetails.staff_id
    );
    let homeData = {};
    let roomList = await homeModel.getRoomList(compnayDetails.company_id);
    let exceptionList = await homeModel.getExceptionList();
    let locationList = await homeModel.getLocationList();
    let genericQr = await qrCodeModel.genericQrListForJobList(
      compnayDetails.company_id
    );
    let itemTagList = await qrCodeModel.itemTagList(compnayDetails.company_id);

    homeData["roomList"] = roomList;
    homeData["exceptionList"] = exceptionList;
    homeData["locationList"] = locationList;
    homeData["genericQr"] = genericQr;
    homeData["itemTagList"] = itemTagList;

    response.status(SUCCESS_CODE).json({
      status: 1,
      message: JOB_RETRIEVED_SUCCESS,
      data: homeData,
    });
  } catch (error) {
    response.status(EXPECTATION_FAILED_CODE).json({
      status: 0,
      message: JOB_NOT_FOUND,
      data: {},
    });
  }
};

exports.getJobList = async (request, response) => {
  try {
    request.query.search = request.query.search ? request.query.search : "";
    let headerData = await commonFunction.jwtTokenDecode(
      request.headers.access_token
    );
    let homeData = {};
    if (request.body.date === "null") {
      let deleteJobList = [];
      let completeJobList = [];

      let jobList = await homeModel.getUpdatedJobList(
        request.body,
        request.query,
        headerData.payload.user_id
      );

      homeData["totalPages"] = Math.ceil(jobList.count / JOB_LIST_PER_PAGE);
      homeData["jobList"] = jobList.rows;
      homeData["deleteJobList"] = deleteJobList;
      homeData["completeJobList"] = completeJobList;

      if (jobList !== "") {
        response.status(SUCCESS_CODE).json({
          status: 1,
          message: JOB_RETRIEVED_SUCCESS,
          data: homeData,
        });
      } else {
        response.status(EXPECTATION_FAILED_CODE).json({
          status: 0,
          message: JOB_NOT_FOUND,
          data: {},
        });
      }
    } else {
      let jobList = await homeModel.getUpdatedJobList(
        request.body,
        request.query,
        headerData.payload.user_id
      );
      let deleteJobList = await homeModel.deleteJobList(
        request.body,
        request.query,
        headerData.payload.user_id
      );
      let completeJobList = await homeModel.completeJobList(
        request.body,
        request.query,
        headerData.payload.user_id
      );

      homeData["totalPages"] = Math.ceil(jobList.count / JOB_LIST_PER_PAGE);
      homeData["jobList"] = jobList.rows;
      homeData["deleteJobList"] = deleteJobList.rows;
      homeData["completeJobList"] = completeJobList.rows;

      if (jobList !== "") {
        response.status(SUCCESS_CODE).json({
          status: 1,
          message: JOB_RETRIEVED_SUCCESS,
          data: homeData,
        });
      } else {
        response.status(EXPECTATION_FAILED_CODE).json({
          status: 0,
          message: JOB_NOT_FOUND,
          data: {},
        });
      }
    }
  } catch (error) {
    response.status(EXPECTATION_FAILED_CODE).json({
      status: 0,
      message: JOB_NOT_FOUND,
      data: {},
    });
  }
};

exports.getUnitList = async (request, response) => {
  try {
    let homeData = {};
    let unitList = await homeModel.getUnitList(request.body);
    homeData["unitList"] = unitList.rows;

    if (unitList !== "") {
      response.status(SUCCESS_CODE).json({
        status: 1,
        message: JOB_RETRIEVED_SUCCESS,
        data: homeData,
      });
    } else {
      response.status(EXPECTATION_FAILED_CODE).json({
        status: 0,
        message: JOB_NOT_FOUND,
        data: {},
      });
    }
  } catch (error) {
    response.status(EXPECTATION_FAILED_CODE).json({
      status: 0,
      message: JOB_NOT_FOUND,
      data: {},
    });
  }
};

exports.getQrListForShipment = async (request, response) => {
  try {
    request.query.search = request.query.search ? request.query.search : "";
    let headerData = await commonFunction.jwtTokenDecode(
      request.headers.access_token
    );
    let homeData = {};
    if (request.body.date === "null") {
      let deleteQrList = [];
      let updateQrList = [];
      let jobList = await homeModel.getJobList(
        request.body,
        request.query,
        headerData.payload.user_id
      );
      homeData["addQrList"] = jobList;
      homeData["deleteQrList"] = deleteQrList;
      homeData["updateQrList"] = updateQrList;

      if (jobList !== "") {
        response.status(SUCCESS_CODE).json({
          status: 1,
          message: JOB_RETRIEVED_SUCCESS,
          data: homeData,
        });
      } else {
        response.status(EXPECTATION_FAILED_CODE).json({
          status: 0,
          message: JOB_NOT_FOUND,
          data: {},
        });
      }
    } else {
      let jobList = await homeModel.getJobList2(
        request.body,
        request.query,
        headerData.payload.user_id
      );
      let deleteQrList = await homeModel.deleteQrList(
        request.body,
        request.query,
        headerData.payload.user_id
      );
      let updateQrList = await homeModel.updateQrList(
        request.body,
        request.query,
        headerData.payload.user_id
      );

      homeData["totalPages"] = Math.ceil(jobList.count / JOB_LIST_PER_PAGE);
      homeData["addQrList"] = jobList.rows;
      homeData["deleteQrList"] = deleteQrList.rows;
      homeData["updateQrList"] = updateQrList.rows;

      if (jobList !== "") {
        response.status(SUCCESS_CODE).json({
          status: 1,
          message: JOB_RETRIEVED_SUCCESS,
          data: homeData,
        });
      } else {
        response.status(EXPECTATION_FAILED_CODE).json({
          status: 0,
          message: JOB_NOT_FOUND,
          data: {},
        });
      }
    }
  } catch (error) {
    response.status(EXPECTATION_FAILED_CODE).json({
      status: 0,
      message: JOB_NOT_FOUND,
      data: {},
    });
  }
};

exports.bulkitemAddInStorage = async (
  request,
  response,
  itemArray,
  consumerLoginJson
) => {
  const stringArray = itemArray.map(String);
  const itemAddInStorageJson = JSON.stringify({
    shipment_inventory_id: stringArray,
    storage_unit_id: request.body.storage_unit_id,
  });
  try {
    const itemAddInStorageResponse = await axios.post(
      `${MOVER_STORAGE_API_URL}import/mover-inventory/add-items`,
      itemAddInStorageJson,
      {
        headers: {
          "Content-Type": "application/json",
          accessToken: consumerLoginJson.data.accessToken,
        },
      }
    );
    if (
      itemAddInStorageResponse.data !== "" &&
      itemAddInStorageResponse.data !== undefined &&
      itemAddInStorageResponse.data !== null
    ) {
      return itemAddInStorageResponse.data;
    } else {
      commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        "Item Add Fail",
        {}
      );
    }
  } catch (error) {
    commonFunction.generateResponse(
      response,
      SERVER_ERROR_CODE,
      0,
      "Item Add Fail",
      {}
    );
  }
};

exports.itemAddInStorage = async (
  request,
  response,
  Inventory_id,
  consumerLoginJson
) => {
  const itemAddInStorageJson = JSON.stringify({
    shipment_inventory_id: Inventory_id,
    storage_unit_id: request.body.storage_unit_id,
  });

  try {
    const itemAddInStorageResponse = await axios.post(
      `${MOVER_STORAGE_API_URL}import/mover-inventory/add-item`,
      itemAddInStorageJson,
      {
        headers: {
          "Content-Type": "application/json",
          accessToken: consumerLoginJson.data.accessToken,
        },
      }
    );
    if (
      itemAddInStorageResponse.data !== "" &&
      itemAddInStorageResponse.data !== undefined &&
      itemAddInStorageResponse.data !== null
    ) {
      return itemAddInStorageResponse.data;
    } else {
      commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        "Item Add Fail",
        {}
      );
    }
  } catch (error) {
    commonFunction.generateResponse(
      response,
      SERVER_ERROR_CODE,
      0,
      "Item Add Fail",
      {}
    );
  }
};

exports.addItem = async (request, response) => {
  try {
    let getUserDetails = await commonModel.getUserDetails(request);
    let newFileName = "";
    let thumbnailImageName = "";
    let isThumbnailImageYes = false;
    const newFileList = [];
    let headerData = await commonFunction.jwtTokenDecode(
      request.headers.access_token
    );
    const user_id = headerData.payload.user_id;
    if (
      request.files &&
      Object.keys(request.files).length > 0 &&
      Object.keys(request.files).length <= 10
    ) {
      for (let key in request.files) {
        let element = request.files[key];

        for (let photo in element) {
          if (!element.hasOwnProperty(photo)) continue;
          // code for image upload on s3
          const compressedImage = await sharp(element[photo].path)
            // .resize({
            // 	width: 50,
            // 	height: 50
            // })
            .jpeg({ quality: 10 })
            .toFile(
              path.resolve("temp", element[photo].filename + "thumbnail")
            );

          const compressedImagePath = path.resolve(
            "temp",
            element[photo].filename + "thumbnail"
          );
          const fileName = element[photo].filename;
          const fileExt = element[photo].originalname.split(".");
          newFileName = fileName + "." + fileExt[1];

          if (
            request.body.item_thumbnail_photo !== undefined &&
            request.body.item_thumbnail_photo !== "" &&
            request.body.item_thumbnail_photo !== null
          ) {
            if (element[photo].fieldname == request.body.item_thumbnail_photo) {
              thumbnailImageName = newFileName;
              isThumbnailImageYes = true;
            }
          }
          // thumbnailFileName = fileName + ".jpg";
          const s3 = new AWS.S3();
          let params = {
            ACL: "public-read",
            Bucket: Const_AWS_BUCKET,
            Body: fs.createReadStream(element[photo].path),
            Key:
              Const_AWS_Job_Item +
              request.body.job_id +
              "/original/" +
              newFileName,
          };
          let result = await commonFunction.UploadImageS3(params, s3);

          let paramsData = {
            ACL: "public-read",
            Bucket: Const_AWS_BUCKET,
            Body: fs.createReadStream(compressedImagePath),
            Key:
              Const_AWS_Job_Item_thumbnail +
              request.body.job_id +
              "/original/" +
              newFileName,
          };
          let resultData = await commonFunction.UploadImageS3(paramsData, s3);
          if (result) {
            fs.unlinkSync(element[photo].path); // Empty temp folder
          }
          newFileList.push({
            media: newFileName,
          });
          // newFileList.push(newFileName);
        }
      }
    } else if (Object.keys(request.files).length > 10) {
      return response.status(NOT_VALID_DATA_CODE).json({
        status: 0,
        message: "You can upload max 10 images.",
        data: {},
      });
    }

    const itemDetail = await homeModel.addItem(
      request.body,
      newFileList,
      isThumbnailImageYes,
      thumbnailImageName
    );
    let inventoryIdForTag = itemDetail.shipment_inventory_id
      ? itemDetail.shipment_inventory_id
      : itemDetail[0].shipment_inventory_id;
    if (request.body.tag) {
      let tagItem = [];
      request.body.tag.map((tag) =>
        tagItem.push({
          shipment_inventory_id: inventoryIdForTag,
          tag_id: tag,
        })
      );
      await homeModel.addTagToInventoryModel(tagItem);
    }
    if (
      request.body.storage_unit_id !== null &&
      request.body.storage_unit_id !== undefined &&
      request.body.storage_unit_id !== ""
    ) {
      let headerData = await commonFunction.jwtTokenDecode(
        request.headers.access_token
      );
      let getStaffCompanyId = await staffModel.getStaffCompanyId(
        headerData.payload.user_id
      );
      const getCompanyDetails = await companyModel.getIntegrationKeyData(
        getStaffCompanyId
      );
      // consumerLoginJson
      const consumerLoginJson = await this.consumerLoginJsonFun(
        request,
        response,
        getCompanyDetails
      );
      if (
        request.files &&
        Object.keys(request.files).length > 0 &&
        Object.keys(request.files).length <= 10
      ) {
        const itemAddInStorage = await this.itemAddInStorage(
          request,
          response,
          itemDetail[0].shipment_inventory_id,
          consumerLoginJson
        );
        const addItemHistory = await homeModel.addItemHistory(
          request.body,
          itemDetail[0].shipment_inventory_id
        );
      } else {
        const itemAddInStorage = await this.itemAddInStorage(
          request,
          response,
          itemDetail.shipment_inventory_id,
          consumerLoginJson
        );
        const addItemHistory = await homeModel.addItemHistory(
          request.body,
          itemDetail.shipment_inventory_id
        );
      }
    }
    if (itemDetail && itemDetail !== "") {
      const updateJobDetail = await homeModel.updateJobDetail(request.body);
    }

    ActionLogModel.createActionLog({
      platform: "APP",
      performed_by_id: getUserDetails.staff_id,
      performed_by_role: "User",
      performed_by_name: getUserDetails.name,
      action_type: "ITEM_CREATE",
      action_performed_on_id: itemDetail.shipment_inventory_id,
      action_performed_on_name: request.body.item_name,
    })

    response.status(SUCCESS_CODE).json({
      status: 1,
      message: ADD_ITEM_SUCCESS,
      data: {},
    });
  } catch (e) {
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: e.message,
      data: {},
    });
  }
};

exports.editItem = async (request, response) => {
  try {
    let getUserDetails = await commonModel.getUserDetails(request);
    let newFileName = "";
    const newFileList = [];
    const oldImageList = [];
    const s3 = new AWS.S3();
    let thumbnailImageName = "";
    let isThumbnailImageYes = false;

    let headerData = await commonFunction.jwtTokenDecode(
      request.headers.access_token
    );
    const user_id = headerData.payload.user_id;
    // get array of images from database for job_id
    const imagesList = await homeModel.getItemImagesList(
      request.body.inventory_id
    );
    // create array of images which is not updated

    for (let index = 0; index < 9; index++) {
      if (
        request.body["photo" + (index === 0 ? "" : index)] &&
        request.body["photo" + (index === 0 ? "" : index)] !== ""
      ) {
        const img =
          request.body["photo" + (index === 0 ? "" : index)].split("original/");
        oldImageList.push(img[1]);
      }
    }
    const updatedOldImages = imagesList.filter(
      (i) => !oldImageList.includes(i.media)
    );
    const checkIsCmsEdit =
      request.body &&
        request.body.itemEditByCms &&
        (request.body.itemEditByCms == true ||
          request.body.itemEditByCms == "true")
        ? true
        : false;
    if (checkIsCmsEdit) {
      console.log("Check:itemEditByCmsTrue");
    } else {
      if (updatedOldImages && updatedOldImages.length > 0) {
        for (let image in updatedOldImages) {
          let delMedia = {
            Bucket: Const_AWS_BUCKET,
            Key:
              Const_AWS_Job_Item +
              request.body.job_id +
              "/original/" +
              updatedOldImages[image].media,
          };
          await commonFunction.deleteImageS3(delMedia, s3);
          const removeImages = await homeModel.removeItemImage(
            updatedOldImages[image].media
          );
        }
      }
    }

    if (
      request.files &&
      Object.keys(request.files).length > 0 &&
      Object.keys(request.files).length <= 10
    ) {
      for (let key in request.files) {
        let element = request.files[key];
        for (let photo in element) {
          if (!element.hasOwnProperty(photo)) continue;
          // code for image upload on s3
          const fileName = element[photo].filename;
          const fileExt = element[photo].originalname.split(".");
          newFileName = fileName + "." + fileExt[1];
          // thumbnailFileName = fileName + ".jpg";
          // const s3 = new AWS.S3();

          if (
            request.body.item_thumbnail_photo !== undefined &&
            request.body.item_thumbnail_photo !== "" &&
            request.body.item_thumbnail_photo !== null
          ) {
            const checkIsOldInventoryPhotoId =
              await homeModel.checkIsOldInventoryPhotoId(
                request.body.item_thumbnail_photo
              );
            if (!checkIsOldInventoryPhotoId) {
              if (
                element[photo].fieldname == request.body.item_thumbnail_photo
              ) {
                thumbnailImageName = newFileName;
                isThumbnailImageYes = true;
              }
            }
          }
          let params = {
            ACL: "public-read",
            Bucket: Const_AWS_BUCKET,
            Body: fs.createReadStream(element[photo].path),
            Key:
              Const_AWS_Job_Item +
              request.body.job_id +
              "/original/" +
              newFileName,
          };
          let result = await commonFunction.UploadImageS3(params, s3);
          if (result) {
            fs.unlinkSync(element[photo].path); // Empty temp folder
          }
          newFileList.push({
            media: newFileName,
          });
          // newFileList.push(newFileName);
        }
      }
    } else if (Object.keys(request.files).length > 10) {
      return response.status(NOT_VALID_DATA_CODE).json({
        status: 0,
        message: "You can upload max 10 images.",
        data: {},
      });
    }

    if (
      request.body.isManualLabel == "true" ||
      request.body.isManualLabel == true
    ) {
      if (request.body.tag) {
        let tagItem = [];
        request.body.tag.map((tag) =>
          tagItem.push({
            shipment_inventory_id: request.body.inventory_id,
            tag_id: tag,
          })
        );
        await homeModel.addTagToInventoryModel(tagItem);
      }

      if (request.body.deleteTag) {
        await homeModel.deleteTagToInventoryModel(request.body.deleteTag);
      }

      const getInventoryDetailsByInventory =
        await homeModel.getInventoryDetailsByLabelNo(request.body.inventory_id);

      if (
        getInventoryDetailsByInventory.label_no ===
        parseInt(request.body.label_no)
      ) {
        const itemDetail = await homeModel.editItem(
          request.body,
          newFileList,
          isThumbnailImageYes,
          thumbnailImageName
        );

        if (
          request.body.storage_unit_id !== null &&
          request.body.storage_unit_id !== undefined &&
          request.body.storage_unit_id !== ""
        ) {
          const editItemHistory = await homeModel.editItemHistory(request.body);
        }

        ActionLogModel.createActionLog({
          platform: "APP",
          performed_by_id: getUserDetails.staff_id,
          performed_by_role: "User",
          performed_by_name: getUserDetails.name,
          action_type: "ITEM_UPDATE",
          action_performed_on_id: request.body.inventory_id,
          action_performed_on_name: request.body.item_name,
        })

        response.status(SUCCESS_CODE).json({
          status: 1,
          message: EDIT_ITEM_SUCCESS,
          data: {},
        });
      } else {
        const getInventoryDetailsByLabelNumber =
          await homeModel.getInventoryDetailsByLabelNumber(
            request.body.job_id,
            request.body.label_no
          );
        if (getInventoryDetailsByLabelNumber) {
          response.status(CONFLICT_CODE).json({
            status: 0,
            message: SHIPMENT_INVENTORY_ALREADY_LABELNUMBER,
            data: {},
          });
        } else {
          const itemDetail = await homeModel.editItem(
            request.body,
            newFileList,
            isThumbnailImageYes,
            thumbnailImageName
          );

          if (
            request.body.storage_unit_id !== null &&
            request.body.storage_unit_id !== undefined &&
            request.body.storage_unit_id !== ""
          ) {
            const editItemHistory = await homeModel.editItemHistory(
              request.body
            );
          }

          ActionLogModel.createActionLog({
            platform: "APP",
            performed_by_id: getUserDetails.staff_id,
            performed_by_role: "User",
            performed_by_name: getUserDetails.name,
            action_type: "ITEM_UPDATE",
            action_performed_on_id: request.body.inventory_id,
            action_performed_on_name: request.body.item_name,
          })


          response.status(SUCCESS_CODE).json({
            status: 1,
            message: EDIT_ITEM_SUCCESS,
            data: {},
          });
        }
      }
    } else {
      if (request.body.tag) {
        let tagItem = [];
        request.body.tag.map((tag) =>
          tagItem.push({
            shipment_inventory_id: request.body.inventory_id,
            tag_id: tag,
          })
        );
        await homeModel.addTagToInventoryModel(tagItem);
      }

      if (request.body.deleteTag) {
        await homeModel.deleteTagToInventoryModel(request.body.deleteTag);
      }

      if (
        request.body.storage_unit_id !== null &&
        request.body.storage_unit_id !== undefined &&
        request.body.storage_unit_id !== ""
      ) {
        const editItemHistory = await homeModel.editItemHistory(request.body);
      }

      const itemDetail = await homeModel.editItem(
        request.body,
        newFileList,
        isThumbnailImageYes,
        thumbnailImageName
      );

      ActionLogModel.createActionLog({
        platform: "APP",
        performed_by_id: getUserDetails.staff_id,
        performed_by_role: "User",
        performed_by_name: getUserDetails.name,
        action_type: "ITEM_UPDATE",
        action_performed_on_id: request.body.inventory_id,
        action_performed_on_name: request.body.item_name,
      })

      response.status(SUCCESS_CODE).json({
        status: 1,
        message: EDIT_ITEM_SUCCESS,
        data: {},
      });
    }
  } catch (e) {
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: e.message,
      data: {},
    });
  }
};

exports.editItemUnit = async (request, response) => {
  try {
    const itemDetail = await homeModel.editItemUnit(request.body);
    let headerData = await commonFunction.jwtTokenDecode(
      request.headers.access_token
    );
    let getStaffCompanyId = await staffModel.getStaffCompanyId(
      headerData.payload.user_id
    );
    const getCompanyDetails = await companyModel.getIntegrationKeyData(
      getStaffCompanyId
    );
    // consumerLoginJson
    const consumerLoginJson = await this.consumerLoginJsonFun(
      request,
      response,
      getCompanyDetails
    );
    const itemAddInStorage = await this.itemAddInStorage(
      request,
      response,
      request.body.inventory_id,
      consumerLoginJson
    );
    const findNewUnitInfo = await homeModel.findNewUnitInfo(request.body);
    if (request.body.isItemAssignToOldUnit) {
      response.status(SUCCESS_CODE).json({
        status: 1,
        message: `Unit item reassigned from ${request.body.oldUnitName} to ${findNewUnitInfo.name} successfully`,
        data: {},
      });
    } else {
      response.status(SUCCESS_CODE).json({
        status: 1,
        message: `Unit ${findNewUnitInfo.name} assigned successfully`,
        data: {},
      });
    }
  } catch (e) {
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: e.message,
      data: {},
    });
  }
};

exports.assignItemUnit = async (request, response) => {
  try {
    const itemDetail = await homeModel.editItemUnit(request.body);
    let headerData = await commonFunction.jwtTokenDecode(
      request.headers.access_token
    );
    let getStaffCompanyId = await staffModel.getStaffCompanyId(
      headerData.payload.user_id
    );
    const getCompanyDetails = await companyModel.getIntegrationKeyData(
      getStaffCompanyId
    );
    // consumerLoginJson
    const consumerLoginJson = await this.consumerLoginJsonFun(
      request,
      response,
      getCompanyDetails
    );
    const itemAddInStorage = await this.itemAddInStorage(
      request,
      response,
      request.body.inventory_id,
      consumerLoginJson
    );
    const findNewUnitInfo = await homeModel.findNewUnitInfo(request.body);
    if (request.body.isItemAssignToOldUnit) {
      response.status(SUCCESS_CODE).json({
        status: 1,
        message: `Unit item reassigned from ${request.body.oldUnitName} to ${findNewUnitInfo.name} successfully`,
        data: {},
      });
    } else {
      response.status(SUCCESS_CODE).json({
        status: 1,
        message: `Unit ${findNewUnitInfo.name} assigned successfully`,
        data: {},
      });
    }
  } catch (e) {
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: e.message,
      data: {},
    });
  }
};

exports.assignBulkItemUnit = async (request, response) => {
  try {
    // const itemDetail = await homeModel.editBulkItemUnit(request.body);
    let headerData = await commonFunction.jwtTokenDecode(
      request.headers.access_token
    );
    let getStaffCompanyId = await staffModel.getStaffCompanyId(
      headerData.payload.user_id
    );
    const getCompanyDetails = await companyModel.getIntegrationKeyData(
      getStaffCompanyId
    );
    // consumerLoginJson
    const consumerLoginJson = await this.consumerLoginJsonFun(
      request,
      response,
      getCompanyDetails
    );
    if (
      request.body.isSelectAllItems == "true" ||
      request.body.isSelectAllItems == true
    ) {
      bulkItemData = await homeModel.findOldUnitOfBulkItemUsingShipment(
        request.body
      );
      // Extract inventory_ids
      const itemArray = bulkItemData.map((row) => row.shipment_inventory_id);
      const itemAddInStorage = await this.bulkitemAddInStorage(
        request,
        response,
        itemArray,
        consumerLoginJson
      );
    } else {
      const itemAddInStorage = await this.bulkitemAddInStorage(
        request,
        response,
        request.body.itemArray,
        consumerLoginJson
      );
    }
    response.status(SUCCESS_CODE).json({
      status: 1,
      message: `Unit assigned successfully`,
      data: {},
    });
  } catch (e) {
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: e.message,
      data: {},
    });
  }
};

exports.removeItem = async (request, response) => {
  try {
    const { inventory_id } = request.params;
    let getUserDetails = await commonModel.getUserDetails(request);
    const s3 = new AWS.S3();
    const jobData = await homeModel.getJobId(inventory_id);
    const itemDetail = await homeModel.getInventoryByinventoryId(inventory_id);
    const imagesList = await homeModel.getItemImagesList(inventory_id);
    if (imagesList) {
      if (imagesList && imagesList.length > 0) {
        for (let image in imagesList) {
          let delMedia = {
            Bucket: Const_AWS_BUCKET,
            Key:
              Const_AWS_Job_Item +
              jobData.shipment_job_id +
              "/original/" +
              imagesList[image].media,
          };
          await commonFunction.deleteImageS3(delMedia, s3);
        }
      }
    }
    if (
      jobData.storage_unit_id !== null &&
      jobData.storage_unit_id !== "" &&
      jobData.storage_unit_id !== undefined
    ) {
      const allUnitItemsWithJob = await homeModel.allUnitItemsWithJob(jobData);

      let homeData = {};
      let headerData = await commonFunction.jwtTokenDecode(
        request.headers.access_token
      );
      let getStaffCompanyId = await staffModel.getStaffCompanyId(
        headerData.payload.user_id
      );
      const getCompanyDetails = await companyModel.getIntegrationKeyData(
        getStaffCompanyId
      );
      // consumerLoginJson
      const consumerLoginJson = await this.consumerLoginJsonFun(
        request,
        response,
        getCompanyDetails
      );
      const removeItemHistory = await homeModel.removeItemHistory(inventory_id);
      const deleteItemStorageJson = await this.deleteItemStorageJson(
        request,
        response,
        inventory_id,
        jobData,
        allUnitItemsWithJob.count == 1,
        consumerLoginJson
      );

      if (allUnitItemsWithJob.count == 1) {
        let emptyUnitUpdateById = await homeModel.emptyUnitUpdateById(
          jobData.storage_unit_id
        );
      }
    }

    const removeItemScanHistoryFind = await homeModel.removeItemScanHistoryFind(
      inventory_id
    );
    if (removeItemScanHistoryFind) {
      const removeItemScanHistoryDelete =
        await homeModel.removeItemScanHistoryDelete(inventory_id);
    }
    const removeItemForcedHistoryFind =
      await homeModel.removeItemForcedHistoryFind(inventory_id);
    if (removeItemScanHistoryFind) {
      const removeItemForcedHistoryDelete =
        await homeModel.removeItemForcedHistoryDelete(inventory_id);
    }
    const removeItem = await homeModel.removeItem(
      inventory_id,
      jobData.inventory_stage_id
    );

    ActionLogModel.createActionLog({
      platform: "APP",
      performed_by_id: getUserDetails.staff_id,
      performed_by_role: "User",
      performed_by_name: getUserDetails.name,
      action_type: "ITEM_DELETE",
      action_performed_on_id: inventory_id,
      action_performed_on_name: itemDetail.item_name,
    })

    response.status(SUCCESS_CODE).json({
      status: 1,
      message: REMOVE_ITEM_SUCCESS,
      data: {},
    });
  } catch (e) {
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: e.message,
      data: {},
    });
  }
};

exports.getJobSummary = async (request, response) => {
  try {
    const jobSummary = await homeModel.getJobSummary(request.body);
    if (jobSummary) {
      response.status(SUCCESS_CODE).json({
        status: 1,
        message: GET_SUMMARY_SUCCESS,
        data: jobSummary,
      });
    } else {
      response.status(NOT_FOUND_CODE).json({
        status: 0,
        message: GET_SUMMARY_EMPTY,
        data: {},
      });
    }
  } catch (e) {
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: e.message,
      data: {},
    });
  }
};

exports.addSignature = async (request, response, next) => {
  try {
    let jobSummary = await homeModel.getJobCurrentJobStatus(request.body);
    request.body.is_current_stage_add_item_to_storage = jobSummary.dataValues.assign_storage_units_to_items === "yes"
    if (
      request.body.current_job_stage < jobSummary.dataValues.current_job_stage
    ) {
      return response.status(BAD_REQUEST_CODE).json({
        status: 0,
        message: "Current job stage already complete!",
        data: {},
      });
    }

    let newFileName = "";
    let headerData = await commonFunction.jwtTokenDecode(
      request.headers.access_token
    );
    if (
      request.files &&
      Object.keys(request.files).length > 0 &&
      Object.keys(request.files).length <= 2
    ) {
      let signatureFiles = {
        customer_signature: null,
        supervisor_signature: null,
      };
      for (let key in request.files) {
        let element = request.files[key];
        for (let photo in element) {
          if (!element.hasOwnProperty(photo)) continue;
          const fileName = element[photo].filename;
          const fileExt = element[photo].originalname.split(".");
          newFileName = fileName + "." + fileExt[1];
          // thumbnailFileName = fileName + ".jpg";
          if (element[photo].fieldname === "signature")
            signatureFiles.supervisor_signature = newFileName;
          else if (element[photo].fieldname === "cust_signature")
            signatureFiles.customer_signature = newFileName;

          const s3 = new AWS.S3();
          let params = {
            ACL: "public-read",
            Bucket: Const_AWS_BUCKET,
            Body: fs.createReadStream(element[photo].path),
            Key:
              Const_AWS_Job_Signature +
              request.body.job_id +
              "/original/" +
              newFileName,
          };
          let result = await commonFunction.UploadImageS3(params, s3);
          if (result) {
            fs.unlinkSync(element[photo].path); // Empty temp folder
          }
        }
      }
      // newFileList.push(newFileName);
      let headerData = await commonFunction.jwtTokenDecode(
        request.headers.access_token
      );
      request.signatureDetail = await homeModel.addSignature(
        request.body,
        signatureFiles,
        headerData.payload.user_id
      );
    } else {
      request.signatureDetail = await homeModel.addSignatureWithoutImage(
        request.body,
        headerData.payload.user_id
      );
    }
    next();
  } catch (e) {
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: e.message,
      data: {},
    });
  }
};

exports.isValidInventoryController = async (request, response, next) => {
  let { inventoryId } = request.params;
  let { inventory_id } = request.body;
  if (
    await homeModel.isValidInventoryModel(
      inventoryId ? inventoryId : inventory_id
    )
  ) {
    next();
  } else {
    commonFunction.generateResponse(
      response,
      NOT_FOUND_CODE,
      0,
      SHIPMENT_INVENTORY_NOT_FOUND,
      {}
    );
  }
};

exports.isValidItemsForAssignUnitController = async (
  request,
  response,
  next
) => {
  let { itemArray, job_id, isSelectAllItems } = request.body;
  if (
    request.body.isSelectAllItems == "true" ||
    request.body.isSelectAllItems == true
  ) {
    next();
  } else {
    let itemList = await homeModel.checkAllItemArrayBelongToShipment(
      itemArray,
      job_id
    );
    if (itemList.length > 0) {
      next();
    } else {
      commonFunction.generateResponse(
        response,
        NOT_FOUND_CODE,
        0,
        "Please scan valid items for shipment",
        {}
      );
    }
  }
};

exports.checkItemsValidationUsingRandomNumber = async (request, response) => {
  try {
    let { random_number } = request.body;
    let { shipmentId } = request.params;
    const isValidQrCode = await homeModel.checkExistQrCodeModel(
      random_number.replace(/\s/g, "")
    );
    if (isValidQrCode) {
      const isValidQrCodeBelongToShipment =
        await homeModel.isValidQrCodeBelongToShipment(
          random_number.replace(/\s/g, ""),
          shipmentId
        );
      if (isValidQrCodeBelongToShipment) {
        const getQrCodeDetialsForCheck =
          await homeModel.getQrCodeDetialsForCheck(
            random_number.replace(/\s/g, "")
          );
        const checkQrCodeBelongToItem = await homeModel.checkQrCodeBelongToItem(
          getQrCodeDetialsForCheck.qr_code_id
        );
        if (checkQrCodeBelongToItem) {
          let inventoryDetails =
            await homeModel.inventoryDetailsForStorageQrcode(
              getQrCodeDetialsForCheck
            );
          commonFunction.generateResponse(
            response,
            SUCCESS_CODE,
            1,
            "Data retrieved successfully!",
            inventoryDetails
          );
        } else {
          commonFunction.generateResponse(
            response,
            NOT_FOUND_CODE,
            0,
            SHIPMENT_INVENTORY_NOT_ASSOC_QR,
            {}
          );
        }
      } else {
        commonFunction.generateResponse(
          response,
          NOT_FOUND_CODE,
          0,
          SHIPMENT_INVENTORY_NOT_ASSOC_SHIPMENT,
          {}
        );
      }
    } else
      commonFunction.generateResponse(
        response,
        NOT_FOUND_CODE,
        0,
        QRCODE_NOT_FOUND,
        {}
      );
  } catch (error) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
  }
};

exports.isValidRandomNumberForAssignUnitController = async (
  request,
  response,
  next
) => {
  let { random_number, job_id } = request.body;
  let getQrIdByRandomNumber = await qrCodeModel.getQrIdByRandomNumber(
    random_number.replace(/\s/g, "")
  );
  if (getQrIdByRandomNumber) {
    let getInventoryByQrId = await homeModel.getInventoryByQrId(
      getQrIdByRandomNumber.qr_code_id
    );
    if (getInventoryByQrId) {
      let checkInvnetoryWithShipment =
        await homeModel.isValidInventoryWithJobModel(
          getInventoryByQrId.shipment_inventory_id,
          job_id
        );
      if (checkInvnetoryWithShipment) {
        request.body.inventory_id = getInventoryByQrId.shipment_inventory_id;
        next();
      } else {
        commonFunction.generateResponse(
          response,
          NOT_FOUND_CODE,
          0,
          SHIPMENT_INVENTORY_NOT_ASSOC_JOB,
          {}
        );
      }
    } else {
      commonFunction.generateResponse(
        response,
        NOT_FOUND_CODE,
        0,
        RANDOM_NUMBER_NOT_ASSOCIATED_WITH_SHIPMENT,
        {}
      );
    }
  } else {
    commonFunction.generateResponse(
      response,
      NOT_FOUND_CODE,
      0,
      RANDOM_NUMBER_DETAILS_NOT_FOUND,
      {}
    );
  }
};

exports.isValidInventoryForRandomNumberController = async (
  request,
  response,
  next
) => {
  let { random_number } = request.params;
  let getQrIdByRandomNumber = await qrCodeModel.getQrIdByRandomNumber(
    random_number.replace(/\s/g, "")
  );
  if (getQrIdByRandomNumber) {
    let getInventoryByQrId = await homeModel.getInventoryByQrId(
      getQrIdByRandomNumber.qr_code_id
    );
    if (getInventoryByQrId) {
      if (
        await homeModel.isValidInventoryModel(
          getInventoryByQrId.shipment_inventory_id
        )
      ) {
        next();
      } else {
        commonFunction.generateResponse(
          response,
          NOT_FOUND_CODE,
          0,
          SHIPMENT_INVENTORY_NOT_FOUND,
          {}
        );
      }
    } else {
      commonFunction.generateResponse(
        response,
        NOT_FOUND_CODE,
        0,
        RANDOM_NUMBER_NOT_ASSOCIATED_WITH_SHIPMENT,
        {}
      );
    }
  } else {
    commonFunction.generateResponse(
      response,
      NOT_FOUND_CODE,
      0,
      RANDOM_NUMBER_DETAILS_NOT_FOUND,
      {}
    );
  }
};

exports.isCheckInventoryController = async (request, response, next) => {
  let { inventoryId } = request.body;
  if (
    await homeModel.isValidInventoryModel(
      inventoryId ? inventoryId : inventory_id
    )
  ) {
    next();
  } else {
    commonFunction.generateResponse(
      response,
      NOT_FOUND_CODE,
      0,
      SHIPMENT_INVENTORY_NOT_FOUND,
      {}
    );
  }
};

exports.isPreScannedInventoryController = async (request, response, next) => {
  const { current_job_stage } = request.body;
  const { shipmentId, inventoryId } = request.params;
  if (
    await homeModel.isScannedInventoryModel(
      shipmentId,
      inventoryId,
      current_job_stage
    )
  ) {
    next();
  } else {
    commonFunction.generateResponse(
      response,
      NOT_VALID_DATA_CODE,
      0,
      SHIPMENT_INVENTORY_ALREADY_SCANNED,
      {}
    );
  }
};

exports.isPreScannedInventoryForRandomNumberController = async (
  request,
  response,
  next
) => {
  const { current_job_stage } = request.body;
  const { shipmentId, random_number } = request.params;
  let getQrIdByRandomNumber = await qrCodeModel.getQrIdByRandomNumber(
    random_number.replace(/\s/g, "")
  );
  let getInventoryByQrId = await homeModel.getInventoryByQrId(
    getQrIdByRandomNumber.qr_code_id
  );
  let inventoryId = getInventoryByQrId.shipment_inventory_id;
  if (
    await homeModel.isScannedInventoryModel(
      shipmentId,
      inventoryId,
      current_job_stage
    )
  ) {
    next();
  } else {
    commonFunction.generateResponse(
      response,
      NOT_VALID_DATA_CODE,
      0,
      SHIPMENT_INVENTORY_ALREADY_SCANNED,
      {}
    );
  }
};

exports.isInventoryOnJobController = async (request, response, next) => {
  const { current_job_stage } = request.body;
  const { shipmentId, inventoryId } = request.params;
  if (
    await homeModel.isInventoryOnJobModel(
      shipmentId,
      inventoryId,
      current_job_stage
    )
  ) {
    next();
  } else {
    commonFunction.generateResponse(
      response,
      NOT_VALID_DATA_CODE,
      0,
      SHIPMENT_INVENTORY_NOT_ASSOC_JOB,
      {}
    );
  }
};

exports.isInventoryOnJobForRandomNumberController = async (
  request,
  response,
  next
) => {
  const { current_job_stage } = request.body;
  const { shipmentId, random_number } = request.params;
  let getQrIdByRandomNumber = await qrCodeModel.getQrIdByRandomNumber(
    random_number.replace(/\s/g, "")
  );
  let getInventoryByQrId = await homeModel.getInventoryByQrId(
    getQrIdByRandomNumber.qr_code_id
  );
  let inventoryId = getInventoryByQrId.shipment_inventory_id;
  if (
    await homeModel.isInventoryOnJobModel(
      shipmentId,
      inventoryId,
      current_job_stage
    )
  ) {
    next();
  } else {
    commonFunction.generateResponse(
      response,
      NOT_VALID_DATA_CODE,
      0,
      SHIPMENT_INVENTORY_NOT_ASSOC_JOB,
      {}
    );
  }
};

exports.isPreOverriddenInventoryController = async (
  request,
  response,
  next
) => {
  const { current_job_stage } = request.body;
  const { shipmentId, inventoryId } = request.params;
  if (
    await homeModel.isPreOverriddenInventoryModel(
      shipmentId,
      inventoryId,
      current_job_stage
    )
  ) {
    next();
  } else {
    commonFunction.generateResponse(
      response,
      NOT_VALID_DATA_CODE,
      0,
      SHIPMENT_INVENTORY_OVERRRIDDEN,
      {}
    );
  }
};

exports.isPreOverriddenInventoryForRandomNumberController = async (
  request,
  response,
  next
) => {
  const { current_job_stage } = request.body;
  const { shipmentId, random_number } = request.params;
  let getQrIdByRandomNumber = await qrCodeModel.getQrIdByRandomNumber(
    random_number.replace(/\s/g, "")
  );
  let getInventoryByQrId = await homeModel.getInventoryByQrId(
    getQrIdByRandomNumber.qr_code_id
  );
  let inventoryId = getInventoryByQrId.shipment_inventory_id;
  if (
    await homeModel.isPreOverriddenInventoryModel(
      shipmentId,
      inventoryId,
      current_job_stage
    )
  ) {
    next();
  } else {
    commonFunction.generateResponse(
      response,
      NOT_VALID_DATA_CODE,
      0,
      SHIPMENT_INVENTORY_OVERRRIDDEN,
      {}
    );
  }
};

exports.scanItemController = async (request, response, next) => {
  try {
    const { current_job_stage, staff_id } = request.body;
    const { shipmentId, inventoryId } = request.params;
    const scannedInventory = await homeModel.scanInventoryJobModel(
      shipmentId,
      inventoryId,
      current_job_stage,
      staff_id
    );
    if (scannedInventory) next();
    else
      return commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        SCAN_NOT_PERFORMED,
        {}
      );
  } catch (reason) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {});
  }
};

exports.scanItemForRandomNumberController = async (request, response, next) => {
  try {
    const { current_job_stage, staff_id } = request.body;
    const { shipmentId, random_number } = request.params;
    let getQrIdByRandomNumber = await qrCodeModel.getQrIdByRandomNumber(
      random_number.replace(/\s/g, "")
    );
    let getInventoryByQrId = await homeModel.getInventoryByQrId(
      getQrIdByRandomNumber.qr_code_id
    );
    let inventoryId = getInventoryByQrId.shipment_inventory_id;
    const scannedInventory = await homeModel.scanInventoryJobModel(
      shipmentId,
      inventoryId,
      current_job_stage,
      staff_id
    );
    await homeModel.updateSingleTotalRemoveItemsToInventory(request.body);
    const removeItemFromInventoryScanned =
      await homeModel.removeItemFromInventoryScanned(
        inventoryId,
        current_job_stage
      );
    if (scannedInventory) next();
    else
      return commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        SCAN_NOT_PERFORMED,
        {}
      );
  } catch (reason) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {});
  }
};

exports.BulkScanItemsController = async (request, response, next) => {
  try {
    const { current_job_stage, staff_id, shipment_id, isSelectAllItems } =
      request.body;
    let bulkItemData;

    if (
      request.body.isSelectAllItems == "true" ||
      request.body.isSelectAllItems == true
    ) {
      bulkItemData =
        await homeModel.findOldUnitOfBulkItemUsingShipmentIdWhichNotScanned(
          request.body
        );
    } else {
      bulkItemData = await homeModel.findOldUnitOfBulkItemNotScanned(
        request.body
      );
    }

    for (let i = 0; i < bulkItemData.length; i++) {
      const itemData = bulkItemData[i];
      const isAlreadyScanCheck = await homeModel.isAlreadyScanCheckModel(
        itemData.shipment_inventory_id,
        current_job_stage,
        shipment_id
      );
      if (isAlreadyScanCheck) {
        const scannedInventory = await homeModel.scanInventoryJobModel(
          shipment_id,
          itemData.shipment_inventory_id,
          current_job_stage,
          staff_id
        );
        const removeItemFromInventoryScanned =
          await homeModel.removeItemFromInventoryScanned(
            itemData.shipment_inventory_id,
            current_job_stage
          );
        let updateShipmentInventoryScanFlag =
          homeModel.updateShipmentInventoryScanFlag(
            itemData.shipment_inventory_id
          );
        await homeModel.updateSingleTotalRemoveItemsToInventory(request.body);
      }
    }
    next();
  } catch (reason) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {});
  }
};

exports.BulkAdditionalItemsScanController = async (request, response, next) => {
  try {
    const { isSelectAllItems, isAddItemsToInventoryScan } = request.body;
    let bulkItemData;

    if (
      isAddItemsToInventoryScan == "true" ||
      isAddItemsToInventoryScan == true
    ) {
      bulkItemData =
        isSelectAllItems == "true" || isSelectAllItems == true
          ? await homeModel.findBulkAdditionalItemsToScanWhichNotScanned(
            request.body
          )
          : await homeModel.findBulkAdditionalItemsToScan(request.body);

      await homeModel.updateBulkItems(request.body, bulkItemData, true);
    } else {
      bulkItemData =
        isSelectAllItems == "true" || isSelectAllItems == true
          ? await homeModel.findBulkRemoveItemsToScanWhichNotScanned(
            request.body
          )
          : await homeModel.findBulkAdditionalItemsToScan(request.body);
      await homeModel.updateBulkItems(request.body, bulkItemData, false);
    }
    next();
  } catch (reason) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {});
  }
};

exports.overrideItemController = async (request, response, next) => {
  try {
    const { current_job_stage, reason, staff_id } = request.body;
    const { shipmentId, inventoryId } = request.params;
    const overriddenInventory = await homeModel.overrideItemModel(
      shipmentId,
      inventoryId,
      current_job_stage,
      reason,
      staff_id
    );
    await homeModel.updateSingleTotalRemoveItemsToInventory(request.body);
    const removeItemFromInventoryScanned =
      await homeModel.removeItemFromInventoryScanned(
        inventoryId,
        current_job_stage
      );
    if (overriddenInventory) next();
    else
      return commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        OVERRIDE_ITEM_FAILED,
        {}
      );
  } catch (reason) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {});
  }
};

exports.unassignBulkItemUnit = async (request, response) => {
  try {
    let getUserDetails = await commonModel.getUserDetails(request);
    let {
      current_job_stage,
      shipment_id,
      staff_id,
      itemArray,
      isSelectAllItems,
    } = request.body;

    let bulkItemData;

    if (
      request.body.isSelectAllItems == "true" ||
      request.body.isSelectAllItems == true
    ) {
      bulkItemData =
        await homeModel.findOldUnitOfBulkItemUsingShipmentIdForUnassign(
          request.body
        );
      itemArray = bulkItemData.map((row) => row.shipment_inventory_id);
    } else {
      bulkItemData = await homeModel.findOldUnitOfBulkItem(request.body);
    }

    let emptyUnitsArray = [];
    let isShipmentMarkedAsDelivered = false;

    for (let i = 0; i < bulkItemData.length; i++) {
      console.log(bulkItemData[i].unit_list);
      const inventoryId = bulkItemData[i].shipment_inventory_id;
      const storageUnitId = bulkItemData[i].storage_unit_id;
      if (
        storageUnitId !== null &&
        storageUnitId !== "" &&
        storageUnitId !== undefined
      ) {
        await homeModel.updateSingleTotalRemoveItemsToStorage(request.body);
      }
      await homeModel.updateShipmentInventoryScanFlagRemoveFromStorage(
        inventoryId
      );
      const [
        getAllShipmentInventoryWithSameStorageUnitId,
        getAllShipmentsWithShipmentJobId,
      ] = await Promise.all([
        homeModel.getAllShipmentInventories(storageUnitId, shipment_id),
        homeModel.getAllShipmentsWithShipmentJobId(shipment_id),
      ]);
      if (
        getAllShipmentInventoryWithSameStorageUnitId &&
        getAllShipmentInventoryWithSameStorageUnitId.length
      ) {
        if (getAllShipmentInventoryWithSameStorageUnitId.length === 1) {
          await homeModel.emptyUnitUpdateById(storageUnitId);
          if (!emptyUnitsArray.includes(storageUnitId)) {
            emptyUnitsArray.push(storageUnitId);
          }
        }
        if (getAllShipmentsWithShipmentJobId.length === 1) {
          await homeModel.emptyUnitUpdateById(storageUnitId);
          isShipmentMarkedAsDelivered = true;
          if (!emptyUnitsArray.includes(storageUnitId)) {
            emptyUnitsArray.push(storageUnitId);
          }
        }
      }
      const updateShipmentInventoryWithDelivered =
        await homeModel.updateShipmentInventoryWithShipmentInventoryId(
          inventoryId,
          current_job_stage
        );
      ActionLogModel.createActionLog({
        platform: "APP",
        performed_by_id: staff_id,
        performed_by_role: "User",
        performed_by_name: getUserDetails.name,
        action_type: "UNIT_REMOVED",
        action_performed_on_id: inventoryId,
        action_performed_on_name: `Item - ${bulkItemData[i].item_name}, Unit - ${bulkItemData[i].unit_list.name} `,
      })
      const removeStageItemStorageHistory =
        await homeModel.removeStageItemStorageHistory(
          inventoryId,
          current_job_stage
        );
    }
    const getCompanyDetails = await companyModel.getIntegrationKeyData(
      request.body
    );
    const consumerLoginJson = await this.consumerLoginJsonFun(
      request,
      response,
      getCompanyDetails
    );
    let shipmentInventoryDetails = {
      itemArray: itemArray,
      emptyUnitsArray: emptyUnitsArray,
      isShipmentMarkedAsDelivered: isShipmentMarkedAsDelivered,
      shipment_job_id: shipment_id,
    };
    const scanOutBulkItemFromStorage = await this.scanOutBulkItemFromStorage(
      shipmentInventoryDetails,
      response,
      consumerLoginJson
    );
    response.status(SUCCESS_CODE).json({
      status: 1,
      message: "Item scanned out of unit successfully!",
      data: {},
    });
  } catch (e) {
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: e.message,
      data: {},
    });
  }
};

exports.isAllBulkItemsScannedController = async (request, response) => {
  try {
    const { current_job_stage, shipment_id } = request.body;
    homeModel
      .isAllInventoryJobItemScannedModel(shipment_id, current_job_stage)
      .then((scanDetails) =>
        commonFunction.generateResponse(
          response,
          SUCCESS_CODE,
          1,
          SCAN_COMPLETED,
          scanDetails
        )
      )
      .catch((reason) =>
        commonFunction.generateResponse(
          response,
          SERVER_ERROR_CODE,
          0,
          reason,
          SCAN_NOT_PERFORMED
        )
      );
  } catch (reason) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {});
  }
};

exports.isAllBulkAdditionalItemsScanController = async (request, response) => {
  try {
    const { shipment_id, isAddItemsToInventoryScan } = request.body;
    homeModel
      .isAllBulkAdditionalItemsScanModel(shipment_id, isAddItemsToInventoryScan)
      .then((scanDetails) =>
        commonFunction.generateResponse(
          response,
          SUCCESS_CODE,
          1,
          SCAN_COMPLETED,
          scanDetails
        )
      )
      .catch((reason) =>
        commonFunction.generateResponse(
          response,
          SERVER_ERROR_CODE,
          0,
          reason,
          SCAN_NOT_PERFORMED
        )
      );
  } catch (reason) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {});
  }
};

exports.isAllScannedForRandomNumberController = async (request, response) => {
  try {
    const { current_job_stage } = request.body;
    const { shipmentId, stageId, random_number } = request.params;
    let getQrIdByRandomNumber = await qrCodeModel.getQrIdByRandomNumber(
      random_number.replace(/\s/g, "")
    );
    let getInventoryByQrId = await homeModel.getInventoryByQrId(
      getQrIdByRandomNumber.qr_code_id
    );
    let inventoryId = getInventoryByQrId.shipment_inventory_id;
    let updateShipmentInventoryScanFlag =
      homeModel.updateShipmentInventoryScanFlag(inventoryId);
    const scanDetails = await homeModel.isAllInventoryJobItemScannedModel(
      shipmentId,
      current_job_stage ? current_job_stage : stageId
    );
    if (scanDetails) {
      scanDetails.InventoryId = getInventoryByQrId.shipment_inventory_id;
      commonFunction.generateResponse(
        response,
        SUCCESS_CODE,
        1,
        SCAN_COMPLETED,
        scanDetails
      );
    } else {
      commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        reason,
        SCAN_NOT_PERFORMED
      );
    }
  } catch (reason) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {});
  }
};

exports.isAllScannedControllerCheck = (request, response) => {
  try {
    const { current_job_stage } = request.body;
    const { shipmentId, stageId } = request.params;
    homeModel
      .isAllInventoryJobItemScannedModel(
        shipmentId,
        current_job_stage ? current_job_stage : stageId
      )
      .then((scanDetails) =>
        commonFunction.generateResponse(
          response,
          SUCCESS_CODE,
          1,
          SCAN_COMPLETED,
          scanDetails
        )
      )
      .catch((reason) =>
        commonFunction.generateResponse(
          response,
          SERVER_ERROR_CODE,
          0,
          reason,
          SCAN_NOT_PERFORMED
        )
      );
  } catch (reason) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {});
  }
};

exports.isAllScannedController = async (request, response) => {
  try {
    const { current_job_stage } = request.body;
    const { shipmentId, stageId, inventoryId } = request.params;
    let getInventoryByQrId = await homeModel.getInventoryByinventoryId(
      inventoryId
    );
    let updateShipmentInventoryScanFlag =
      homeModel.updateShipmentInventoryScanFlag(inventoryId);
    const scanDetails = await homeModel.isAllInventoryJobItemScannedModel(
      shipmentId,
      current_job_stage ? current_job_stage : stageId
    );
    if (scanDetails) {
      scanDetails.InventoryId = getInventoryByQrId.shipment_inventory_id;
      commonFunction.generateResponse(
        response,
        SUCCESS_CODE,
        1,
        SCAN_COMPLETED,
        scanDetails
      );
    } else {
      commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        reason,
        SCAN_NOT_PERFORMED
      );
    }
  } catch (reason) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {});
  }
};

exports.movegisticsWebhookJsonFun = async (
  request,
  response,
  requestDetails
) => {
  try {
    const movegisticsWebhookResponse = await axios.post(
      `${MOVEGISTICS_STORAGE_API_URL}storage/webhook/moverInventory`,
      requestDetails,
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    if (
      movegisticsWebhookResponse.data !== "" &&
      movegisticsWebhookResponse.data !== undefined &&
      movegisticsWebhookResponse.data !== null
    ) {
      return movegisticsWebhookResponse.data;
    } else {
      commonFunction.generateResponse(
        response,
        SERVER_ERROR_CODE,
        0,
        "Movegistics webhook error",
        {}
      );
    }
  } catch (error) {
    commonFunction.generateResponse(
      response,
      SERVER_ERROR_CODE,
      0,
      "Movegistics webhook error",
      {}
    );
  }
};

exports.getJobListController = async (request, response) => {
  try {
    let { job_id, stage, is_current_stage_add_item_to_storage } = request.body;
    let headerData = await commonFunction.jwtTokenDecode(
      request.headers.access_token
    );
    let getUserDetails = await commonModel.getUserDetails(request);
    const stageDetails = await shipmentModel.getShipmentStageDetails(
      stage
    );
    if (is_current_stage_add_item_to_storage) {
      await homeModel.updateShipmentInventoryWithAssignUnitCompleted(
        stage
      );
    }
    const jobDetail = await homeModel.getJobSpecific(
      job_id,
      headerData.payload.user_id
    );
    if (jobDetail) {
      const scanDetails = await homeModel.isAllInventoryJobItemScannedModel(
        request.body.job_id,
        jobDetail.job_status
      );
      // const chnageScanItemStatusOfJob = homeModel.chnageScanItemStatusOfJob(job_id);
      const updatedDetails = { ...jobDetail.toJSON(), ...scanDetails };
      // const fetchCompanyKeyDetails = await companyModel.fetchCompanyKeyDetails(jobDetail.company_id)
      // if (fetchCompanyKeyDetails && fetchCompanyKeyDetails.isEnable) {
      // 	const jobSummary = await homeModel.getJobSummaryMovegistics(job_id);
      // 	const data = await this.movegisticsWebhookJsonFun(request, response, jobSummary);
      // }
      // Determine the appropriate total count based on stage type
      let totalCount = 0;
      if (stageDetails.add_items_to_inventory) {
        totalCount = stageDetails.total_add_items_inventory_stage;
      } else if (stageDetails.assign_storage_units_to_items) {
        totalCount = stageDetails.total_add_items_storage_stage;
      } else if (stageDetails.unassign_storage_units_from_items) {
        totalCount = stageDetails.total_remove_items_storage_stage;
      } else if (stageDetails.remove_items_to_inventory) {
        totalCount = stageDetails.total_remove_items_inventory_stage;
      } else if (stageDetails.scan_into_storage) {
        totalCount = stageDetails.scan_require;
      } else if (stageDetails.total_remove_items_to_inventory_scan) {
        totalCount = stageDetails.add;
      } else {
        totalCount = 0;
      }

      ActionLogModel.createActionLog({
        platform: "APP",
        performed_by_id: getUserDetails.staff_id,
        performed_by_role: "User",
        performed_by_name: getUserDetails.name,
        action_type: "SHIPMENT_STAGE_COMPLETED",
        action_performed_on_id: job_id,
        action_performed_on_name: `Shipment - ${jobDetail.job_number}, Stage - ${stageDetails.name}(${totalCount})`,
        total_count: totalCount
      })
      return commonFunction.generateResponse(
        response,
        SUCCESS_CODE,
        1,
        GENERIC_REQUEST_SUCCESS,
        updatedDetails
      );
    } else
      return commonFunction.generateResponse(
        response,
        NOT_FOUND_CODE,
        0,
        ADD_SIGNATURE_FAIL,
        {}
      );
  } catch (reason) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {});
  }
};

exports.getInventoryDetailController = async (request, response) => {
  try {
    const { inventoryId } = request.params;
    const inventoryList = await homeModel.getInventoryDetailModel(inventoryId);
    if (inventoryList) {
      if (request.company_id && inventoryList.shipment_job) {
        if (inventoryList.shipment_job.company_id === request.company_id) {
          return commonFunction.generateResponse(
            response,
            SUCCESS_CODE,
            1,
            GENERIC_REQUEST_SUCCESS,
            inventoryList
          );
        } else {
          return commonFunction.generateResponse(
            response,
            NOT_FOUND_CODE,
            0,
            SHIPMENT_INVENTORY_EMPTY,
            {}
          );
        }
      } else {
        return commonFunction.generateResponse(
          response,
          SUCCESS_CODE,
          1,
          GENERIC_REQUEST_SUCCESS,
          inventoryList
        );
      }
    } else
      return commonFunction.generateResponse(
        response,
        NOT_FOUND_CODE,
        0,
        SHIPMENT_INVENTORY_EMPTY,
        {}
      );
  } catch (reason) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {});
  }
};

exports.fetchInventoryCommentController = async (request, response) => {
  try {
    const { inventoryId } = request.params;
    const inventoryCommentList = await homeModel.getInventoryCommentDetailModel(
      inventoryId
    );
    if (inventoryCommentList) {
      return commonFunction.generateResponse(
        response,
        SUCCESS_CODE,
        1,
        "success",
        inventoryCommentList
      );
    } else {
      return commonFunction.generateResponse(
        response,
        NOT_FOUND_CODE,
        0,
        "Comment list empty!.",
        {}
      );
    }
  } catch (reason) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {});
  }
};

exports.fetchInventoryNoteController = async (request, response) => {
  try {
    const { inventoryId } = request.params;
    const inventoryNoteList = await homeModel.getInventoryNoteDetailModel(
      inventoryId
    );
    if (inventoryNoteList) {
      return commonFunction.generateResponse(
        response,
        SUCCESS_CODE,
        1,
        "success",
        inventoryNoteList
      );
    } else {
      return commonFunction.generateResponse(
        response,
        NOT_FOUND_CODE,
        0,
        "note list empty!.",
        {}
      );
    }
  } catch (reason) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {});
  }
};

exports.addInventoryCommentController = async (request, response) => {
  try {
    const inventoryCommentDetails = await homeModel.addInventoryCommentModel(
      request.body
    );
    if (inventoryCommentDetails)
      commonFunction.generateResponse(
        response,
        SUCCESS_CODE,
        1,
        "Comment added successfully!.",
        inventoryCommentDetails
      );
    else
      commonFunction.generateResponse(
        response,
        SUCCESS_CODE,
        0,
        "Error while adding comment!.",
        {}
      );
  } catch (reason) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {});
  }
};

exports.addInventoryNoteController = async (request, response) => {
  try {
    const inventoryNoteDetails = await homeModel.addInventoryNoteModel(
      request.body
    );
    if (inventoryNoteDetails)
      commonFunction.generateResponse(
        response,
        SUCCESS_CODE,
        1,
        "Note added successfully!.",
        inventoryNoteDetails
      );
    else
      commonFunction.generateResponse(
        response,
        SUCCESS_CODE,
        0,
        "Error while adding note!.",
        {}
      );
  } catch (reason) {
    commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {});
  }
};

exports.addShipmentInventoryMedia = async (request, response) => {
  try {
    try {
      let newFileName = "";
      const newFileList = [];
      let headerData = await commonFunction.jwtTokenDecode(
        request.headers.access_token
      );
      const user_id = headerData.payload.user_id;
      //multiple images upload with different keys
      if (
        request.files &&
        Object.keys(request.files).length > 0 &&
        Object.keys(request.files).length <= 10
      ) {
        for (let key in request.files) {
          let element = request.files[key];
          for (let photo in element) {
            // skip loop if the property is from prototype
            if (!element.hasOwnProperty(photo)) continue;
            // code for image upload on s3
            const fileName = element[photo].filename;
            const fileExt = element[photo].originalname.split(".");
            newFileName = fileName + "." + fileExt[1];
            thumbnailFileName = fileName + ".jpg";
            const s3 = new AWS.S3();
            let params = {
              // ACL: "public-read",
              Bucket: Const_AWS_BUCKET,
              Body: fs.createReadStream(element[photo].path),
              Key:
                Const_AWS_Shipment_inventory +
                request.body.inventory_id +
                "/original/" +
                newFileName,
            };
            let result = await commonFunction.UploadImageS3(params, s3);
            if (result) {
              fs.unlinkSync(element[photo].path); // Empty temp folder
            }
            newFileList.push({
              media: newFileName,
            });
            // newFileList.push(newFileName);
          }
        }
      } else if (Object.keys(request.files).length > 10) {
        return response.status(NOT_VALID_DATA_CODE).json({
          status: 0,
          message: "You can upload max 10 images.",
          data: {},
        });
      }
      const itemDetail = await homeModel.addShipmentInventoryMedia(
        request.body,
        newFileList
      );
      response.status(SUCCESS_CODE).json({
        status: 1,
        message: ADD_SHIPMENT_INVENTORY_MEDIA_SUCCESS,
        data: {},
      });
    } catch (error) {
      response.status(EXPECTATION_FAILED_CODE).json({
        status: 0,
        message: ADD_ITEM_FAIL,
        data: {},
      });
    }
  } catch (e) {
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: e.message,
      data: {},
    });
  }
};

exports.scriptAddItemController = async (request, response) => {
  try {
    const { qty } = request.body;
    for (let i = 0; i < qty; i++) {
      let newFileName = "";
      const newFileList = [];
      if (request.files && Object.keys(request.files).length > 0) {
        for (let key in request.files) {
          let element = request.files[key];
          for (let photo in element) {
            if (!element.hasOwnProperty(photo)) continue;
            const fileName = element[photo].filename;
            const fileExt = element[photo].originalname.split(".");
            newFileName = fileName + "." + fileExt[1];
            const s3 = new AWS.S3();
            let params = {
              ACL: "public-read",
              Bucket: Const_AWS_BUCKET,
              Body: fs.createReadStream(element[photo].path),
              Key:
                Const_AWS_Job_Item +
                request.body.job_id +
                "/original/" +
                newFileName,
            };
            let result = await commonFunction.UploadImageS3(params, s3);
            // if (result) {
            // 	fs.unlinkSync(element[photo].path);
            // }
            newFileList.push({
              media: newFileName,
            });
          }
        }
      }
      const itemDetail = await homeModel.scriptAddItem(
        request.body,
        i,
        newFileList
      );
      const updateJobDetail = await homeModel.updateJobDetail(request.body);
    }
    response.status(SUCCESS_CODE).json({
      status: 1,
      message: ADD_ITEM_SUCCESS,
      data: {},
    });
  } catch (e) {
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: e.message,
      data: {},
    });
  }
};

exports.createBulkUnitListController = async (request, response) => {
  try {
    const unitList = request.body.unitList;
    if (unitList.length > 0) {
      let loopIndex,
        loopJ,
        remainingQuantity,
        chunk = 500;
      for (
        loopIndex = 0, loopJ = unitList.length;
        loopIndex < loopJ;
        loopIndex += chunk
      ) {
        remainingQuantity = unitList.slice(loopIndex, loopIndex + chunk);
        const createBulkUnitList = await homeModel.createBulkUnitList(
          remainingQuantity
        );
      }
      response.status(SUCCESS_CODE).json({
        status: 1,
        message: "Unit created successfully!.",
        data: {},
      });
    } else {
      response.status(SUCCESS_CODE).json({
        status: 0,
        message: "data not found !",
        data: {},
      });
    }
  } catch (error) {
    console.log("exports.createBulkUnitListController -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.shuffleAndReshuffleItemController = async (request, response) => {
  try {
    let { itemIds, shipmentId, unitId, emptyUnitIds } = request.body;
    if (itemIds.length > 0) {
      const unitDetails = await homeModel.shuffleAndReshuffleUnitDetails(
        unitId
      );

      for (let index = 0; index < itemIds.length; index++) {
        const shuffleAndReshuffleItem = await homeModel.shuffleAndReshuffleItem(
          itemIds[index],
          shipmentId,
          unitDetails
        );
        const shuffleAndReshuffleItemHistoryUpdate =
          await homeModel.shuffleAndReshuffleItemHistoryUpdate(
            itemIds[index],
            shipmentId,
            unitDetails
          );
      }

      const newUnitUpdate = await homeModel.newUnitUpdate(unitId, shipmentId);
      const emptyUnitUpdate = await homeModel.emptyUnitUpdate(emptyUnitIds);

      response.status(SUCCESS_CODE).json({
        status: 1,
        message: "Unit update successfully!.",
        data: {},
      });
    } else {
      response.status(SUCCESS_CODE).json({
        status: 0,
        message: "data not found !",
        data: {},
      });
    }
  } catch (error) {
    console.log("exports.shuffleAndReshuffleItemController -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.deliverShipmentController = async (request, response) => {
  try {
    let { shipmentId, unitIds, isCompleteDelivery } = request.body;

    if (unitIds.length > 0) {
      for (let index = 0; index < unitIds.length; index++) {
        const updateDeliverShipmentUnit =
          await homeModel.updateDeliverShipmentUnit(unitIds[index]);
        const updateDeliverShipmentInventory =
          await homeModel.updateDeliverShipmentInventory(
            unitIds[index],
            shipmentId
          );
      }
      if (isCompleteDelivery) {
        const jobCompleteFlag = await homeModel.jobCompleteFlagFromStorage(
          shipmentId
        );
      }
      response.status(SUCCESS_CODE).json({
        status: 1,
        message: "Unit update successfully!.",
        data: {},
      });
    } else {
      response.status(SUCCESS_CODE).json({
        status: 0,
        message: "Units not found !",
        data: {},
      });
    }
  } catch (error) {
    console.log("exports.deliverShipmentController -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.allItemsListForCmsController = async (request, response) => {
  try {
    const shipmentId = request.body.shipmentId;
    request.body.search = request.body.search ? request.body.search : "";
    const { rows, count } = await homeModel.allItemsListModelForCms(
      request.body,
      shipmentId
    );
    const jobDetails = await homeModel.allItemsListJobDetailsModel(shipmentId);

    let sortedRows = rows.slice();

    response.status(SUCCESS_CODE).json({
      status: 1,
      message: "Items retrieved successfully!.",
      data: {
        rows: sortedRows,
        count: count,
        jobDetails,
        pageno: request.body.page_no,
      },
    });
  } catch (error) {
    console.log("exports.allItemsListController -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.allItemsListForCmsWebController = async (request, response) => {
  try {
    const shipmentId = request.body.shipmentId;
    request.body.search = request.body.search ? request.body.search : "";
    const { rows, count } = await homeModel.allItemsListModelForCmsWeb(
      request.body,
      shipmentId
    );
    const jobDetails = await homeModel.allItemsListJobDetailsModel(shipmentId);

    let sortedRows = rows.slice();

    response.status(SUCCESS_CODE).json({
      status: 1,
      message: "Items retrieved successfully!.",
      data: {
        rows: sortedRows,
        count: count,
        jobDetails,
        pageno: request.body.page_no,
      },
    });
  } catch (error) {
    console.log("exports.allItemsListController -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.allItemsListController = async (request, response) => {
  try {
    let homeData = {};
    const shipmentId = request.params.shipmentId
      ? request.params.shipmentId
      : request.body.shipment_id;
    request.query.search = request.query.search ? request.query.search : "";
    const { rows, count } = await homeModel.allItemsListModel(
      request.query,
      shipmentId
    );
    const jobDetails = await homeModel.allItemsListJobDetailsModel(shipmentId);
    response.status(SUCCESS_CODE).json({
      status: 1,
      message: "Items retrieved successfully!.",
      data: {
        rows,
        count: count,
        jobDetails,
        pageno: request.query.page_no,
      },
    });
  } catch (error) {
    console.log("exports.allItemsListController -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.shipmentListForStorageController = async (request, response) => {
  try {
    const { rows, count } = await homeModel.shipmentListForStorageModel(
      request.body
    );
    response.status(SUCCESS_CODE).json({
      status: 1,
      message: "Shipment retrieved successfully!.",
      data: rows,
    });
  } catch (error) {
    console.log("exports.shipmentListForStorageController -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.allItemsListByUnitIdController = async (request, response) => {
  try {
    const { unitArray } = request.body;
    request.query.search = request.query.search ? request.query.search : "";
    const rows = await homeModel.allItemsListByUnitIdModel(
      request.query,
      unitArray
    );
    response.status(SUCCESS_CODE).json({
      status: 1,
      message: "Items retrieved successfully!.",
      data: rows,
    });
  } catch (error) {
    console.log("exports.allItemsListByUnitIdController -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.fetchAllItemListByWarehouse = async (request, response) => {
  try {
    request.body.search = request.body.search ? request.body.search : "";
    const rows = await homeModel.fetchAllItemListByWarehouse(request.body);
    response.status(SUCCESS_CODE).json({
      status: 1,
      message: "Items retrieved successfully!.",
      data: rows,
    });
  } catch (error) {
    console.log("exports.fetchAllItemListByWarehouse -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.allItemsCountByUnitIdController = async (request, response) => {
  try {
    const { rows, count } = await homeModel.allItemsCountByUnitIdController(
      request.body
    );
    response.status(SUCCESS_CODE).json({
      status: 1,
      message: "Items retrieved successfully!.",
      data: rows,
    });
  } catch (error) {
    console.log("exports.allItemsCountByUnitIdController -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.fetchAllUnitCountValidation = async (request, response) => {
  try {
    const { rows, count } = await homeModel.fetchAllUnitCountValidation(
      request.body
    );
    response.status(SUCCESS_CODE).json({
      status: 1,
      message: "Items retrieved successfully!.",
      data: {
        count: count,
      },
    });
  } catch (error) {
    console.log("exports.fetchAllUnitCountValidation -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.scanOutItemFromStorage = async (
  shipmentInventoryDetails,
  response,
  consumerLoginJson
) => {
  const scanOutItemDetails = {
    shipment_inventory_id: shipmentInventoryDetails.shipment_inventory_id,
    storage_unit_id: shipmentInventoryDetails.storage_unit_id,
    shipment_job_id: shipmentInventoryDetails.shipment_job_id,
    isShipmentMarkedAsDelivered:
      shipmentInventoryDetails.isShipmentMarkedAsDelivered,
    isLastItem: shipmentInventoryDetails.isLastItem,
  };
  console.log(
    "🚀 ~ file: homeController.js:2685 ~ exports.scanOutItemFromStorage= ~ scanOutItemDetails:",
    scanOutItemDetails
  );

  try {
    const scanOutItemResponse = await axios.post(
      `${MOVER_STORAGE_API_URL}import/mover-inventory/change-unit-shipment-status`,
      scanOutItemDetails,
      {
        headers: {
          "Content-Type": "application/json",
          accessToken: consumerLoginJson.data.accessToken,
        },
      }
    );
    if (
      scanOutItemResponse.data !== "" &&
      scanOutItemResponse.data !== undefined &&
      scanOutItemResponse.data !== null
    ) {
      return scanOutItemResponse.data;
    }
    // else {
    // 	commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Item Scan out Fail", {});
    // }
  } catch (error) {
    // commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Item Scan out Fail", {});
  }
};

exports.scanOutBulkItemFromStorage = async (
  shipmentInventoryDetails,
  response,
  consumerLoginJson
) => {
  const scanOutItemDetails = {
    shipment_inventory_ids: shipmentInventoryDetails.itemArray,
    empty_storage_unit_ids: shipmentInventoryDetails.emptyUnitsArray,
    isShipmentMarkedAsDelivered:
      shipmentInventoryDetails.isShipmentMarkedAsDelivered,
    shipment_job_id: shipmentInventoryDetails.shipment_job_id,
  };
  try {
    const scanOutItemResponse = await axios.post(
      `${MOVER_STORAGE_API_URL}import/mover-inventory/bulk-unassign-item-units`,
      scanOutItemDetails,
      {
        headers: {
          "Content-Type": "application/json",
          accessToken: consumerLoginJson.data.accessToken,
        },
      }
    );
    if (
      scanOutItemResponse.data !== "" &&
      scanOutItemResponse.data !== undefined &&
      scanOutItemResponse.data !== null
    ) {
      return scanOutItemResponse.data;
    }
  } catch (error) {
    // commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, "Item Scan out Fail", {});
  }
};

exports.updateItemThumbnail = async (request, response) => {
  try {
    const removeItemOldThumbnail = await homeModel.removeItemOldThumbnail(
      request.body.inventory_id
    );
    const updateItemThumbnail = await homeModel.updateItemThumbnail(
      request.body.inventory_photo_id
    );
    response.status(SUCCESS_CODE).json({
      status: 1,
      message: "Thumbnail updated successfully.",
      data: {},
    });
  } catch (error) {
    console.log("exports.updateItemThumbnail -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.companyListByGroup = async (request, response) => {
  try {
    const isGroupExists = await homeModel.isGroupExists(request.body);
    if (isGroupExists) {
      request.body.search = request.body.search ? request.body.search : "";
      const resData = await homeModel.companyListByGroup(request.body);
      response.status(SUCCESS_CODE).json({
        status: 1,
        message: "Data retrieved successfully.",
        data: resData,
      });
    } else {
      response.status(SUCCESS_CODE).json({
        status: 0,
        message: "Please enter valid groupId.",
        data: {},
      });
    }
  } catch (error) {
    console.log("exports.companyListByGroup -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};

exports.companyListBySuperAdmin = async (request, response) => {
  try {
    request.body.search = request.body.search ? request.body.search : "";
    const resData = await homeModel.companyListBySuperAdmin(request.body);
    response.status(SUCCESS_CODE).json({
      status: 1,
      message: "Data retrieved successfully.",
      data: resData,
    });
  } catch (error) {
    console.log("exports.companyListBySuperAdmin -> error: ", error);
    response.status(SERVER_ERROR_CODE).json({
      status: 0,
      message: error.message,
      data: {},
    });
  }
};
