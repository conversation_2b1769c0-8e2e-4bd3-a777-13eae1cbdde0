const itemSuggestionModel = require("../../models/Admin/itemSuggestionModel");
const commonFunction = require("../../assets/common");
const commonModel = require("../../models/Admin/commonModel");
const ActionLogModel = require("../../models/Admin/ActionLogModel");

exports.getItemSuggestionListingController = async (request, response) => {
	try {
		request.query.search = request.query.search ? request.query.search : "";
		let getUserDetails = await commonModel.getUserDetails(request);
		const itemSuggestionListing = await itemSuggestionModel.getItemListingModel(request.query, getUserDetails)
		if (itemSuggestionListing)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ITEM_SUG_RETRIEVED_SUCCESS,
				itemSuggestionListing
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ITEM_SUG_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		console.log("exports.getItemSuggestionListingController -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.createItemSuggestionController = async (request, response) => {
	try {
		const item = request.body;
		let getUserDetails = await commonModel.getUserDetails(request);
		const itemSuggestionDetails = await itemSuggestionModel.createItemSuggestionModel(item, getUserDetails)
		if (itemSuggestionDetails) {
			// Capture affected fields from request payload
			const affectedFields = ActionLogModel.captureAffectedFields(request.body);

			ActionLogModel.createActionLog({
				platform: "CMS",
				performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
				performed_by_name: getUserDetails.name,
				action_type: "ITEM_SUGGESTION_CREATE",
				action_performed_on_id: itemSuggestionDetails.item_suggestion_id,
				action_performed_on_name: itemSuggestionDetails.name,
				affected_fields: affectedFields
			})
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ITEM_SUG_ADDED_SUCCESS,
				itemSuggestionDetails
			);
		}
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ITEM_SUG_ADDED_FAILURE,
				{}
			);
	}
	catch (reason) {
		console.log("exports.createItemSuggestionController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.createItemSuggestionOnTheFlyController = async (
	request,
	response,
	next
) => {
	const item = request.body;
	item.item_volume = parseInt(item.volume);
	item.item_weight = parseInt(item.weight);
	if (item.item_name) {
		const itemDetails = await itemSuggestionModel.getItemViaNameModel(item.item_name)
		let getUserDetails = await commonModel.getUserDetails(request);
		if (!itemDetails) itemSuggestionModel.createItemSuggestionModel(item, getUserDetails);
	}
	next();
};

exports.editItemSuggestionController = async (request, response) => {
	try {
		const { itemId } = request.params;
		const item = request.body;
		let getUserDetails = await commonModel.getUserDetails(request);

		// Get old data before update
		const oldItemData = await itemSuggestionModel.getItemModel(itemId);

		const itemSuggestionDetails = await itemSuggestionModel.editItemModel(itemId, item, getUserDetails)
		if (itemSuggestionDetails) {
			// Capture both old and new values for update operation
			const affectedFields = ActionLogModel.captureUpdateFields(oldItemData, request.body);

			ActionLogModel.createActionLog({
				platform: "CMS",
				performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
				performed_by_name: getUserDetails.name,
				action_type: "ITEM_SUGGESTION_UPDATE",
				action_performed_on_id: itemId,
				action_performed_on_name: item.item_name,
				affected_fields: affectedFields
			})

			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ITEM_SUG_EDIT_SUCCESS,
				itemSuggestionDetails
			);
		}
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ITEM_SUG_EDIT_FAILURE,
				{}
			);
	}
	catch (reason) {
		console.log("exports.editItemSuggestionController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.changeItemStatus = async (request, response) => {
	try {
		const { item_id } = request.body;
		let getUserDetails = await commonModel.getUserDetails(request);
		const findOldStatusitemSuggestion = await itemSuggestionModel.findOldStatusitemSuggestion(item_id);
		const itemSuggestionDetails = await itemSuggestionModel.changeItemStatus(item_id)
		if (itemSuggestionDetails) {

			ActionLogModel.createActionLog({
				platform: "CMS",
				performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
				performed_by_name: getUserDetails.name,
				action_type: findOldStatusitemSuggestion.status == "active" ? "ITEM_SUGGESTION_DEACTIVATE" : "ITEM_SUGGESTION_ACTIVATE",
				action_performed_on_id: item_id,
				action_performed_on_name: findOldStatusitemSuggestion.name,
			})

			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				findOldStatusitemSuggestion.status == "active" ? "Item deactivated successfully" : "Item activated successfully",
				itemSuggestionDetails
			);
		}
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				STATUS_CHANGE_FAIL,
				{}
			);
	}
	catch (reason) {
		console.log("exports.changeItemStatus -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.isValidItemSuggestionController = async (request, response, next) => {
	try {
		const itemId = request.params.itemId
			? request.params.itemId
			: request.body.item_id;

		const isValidItemSuggestion = await itemSuggestionModel.checkItemExistenceModel(itemId);
		if (isValidItemSuggestion) {
			next();
		} else
			commonFunction.generateResponse(
				response,
				NOT_FOUND_CODE,
				0,
				ITEM_SUG_NOT_FOUND,
				{}
			);
	} catch (error) {
		console.log("exports.isValidItemSuggestionController -> error: ", error);

		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};

exports.removeItemController = async (request, response) => {
	try {
		const { itemId } = request.params;
		const findOldStatusitemSuggestion = await itemSuggestionModel.findOldStatusitemSuggestion(itemId)
		const data = await itemSuggestionModel.removeItemModel(itemId)
		let getUserDetails = await commonModel.getUserDetails(request);
		if (data) {

			// Capture affected fields for DELETE operation (store the item suggestion data that was deleted)
			const affectedFields = {
				name: findOldStatusitemSuggestion.name,
				item_name: findOldStatusitemSuggestion.item_name,
				status: findOldStatusitemSuggestion.status
			};

			ActionLogModel.createActionLog({
				platform: "CMS",
				performed_by_id: getUserDetails.admin_id ? getUserDetails.admin_id : getUserDetails.staff_id ? getUserDetails.staff_id : getUserDetails.company_id,
				performed_by_role: getUserDetails.admin_id ? "Admin" : getUserDetails.staff_id ? "User" : "Company",
				performed_by_name: getUserDetails.name,
				action_type: "ITEM_SUGGESTION_DELETE",
				action_performed_on_id: itemId,
				action_performed_on_name: findOldStatusitemSuggestion.name,
				affected_fields: affectedFields
			})
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ITEM_SUG_DELETE_SUCCESS,
				{}
			);
		}
	}
	catch (error) {
		console.log("exports.removeItemController -> error: ", error);

		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};

exports.viewItemController = async (request, response) => {
	try {
		const { itemId } = request.params;
		const itemDetails = await itemSuggestionModel.getItemModel(itemId)
		if (itemDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				ITEM_SUG_DETAILS,
				itemDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				ITEM_SUG_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		console.log("exports.viewItemController -> error: ", reason);

		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};




exports.batchItemListStatusChange = async (request, response) => {
	try {
		let { itemList, isActiveFlag } = request.body;
		await itemSuggestionModel.batchItemListStatusChange(itemList, isActiveFlag);

		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			isActiveFlag ? "Item activated successfully" : "Item deactivated successfully",
			{}
		)
	}
	catch (reason) {
		console.log("exports.batchItemListStatusChange -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
}


exports.batchDeleteItemList = async (request, response) => {
	try {
		let { itemList } = request.body;
		await itemSuggestionModel.batchDeleteItemList(itemList);

		commonFunction.generateResponse(
			response,
			SUCCESS_CODE,
			1,
			ITEM_SUG_DELETE_SUCCESS,
			{}
		);
	}
	catch (reason) {
		console.log("exports.batchDeleteItemList -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
}